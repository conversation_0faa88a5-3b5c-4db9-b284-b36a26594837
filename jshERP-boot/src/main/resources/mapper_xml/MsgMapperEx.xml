<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.MsgMapperEx">
    <resultMap extends="com.jsh.erp.datasource.mappers.MsgMapper.BaseResultMap" id="ResultExMap" type="com.jsh.erp.datasource.entities.MsgEx">
    </resultMap>
    <select id="selectByConditionMsg" parameterType="com.jsh.erp.datasource.entities.MsgExample" resultMap="ResultExMap">
        SELECT *
        FROM jsh_msg
        WHERE 1=1
        and ifnull(delete_Flag,'0') !='1'
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and msg_title like #{bindName}
        </if>
        order by create_time desc
    </select>

    <update id="batchDeleteMsgByIds">
        update jsh_msg
        set delete_Flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="getMsgCountByStatus" resultType="java.lang.Long">
        SELECT
        COUNT(id)
        FROM jsh_msg
        WHERE 1=1
        and ifnull(delete_Flag,'0') !='1'
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="userId != null">
            and user_id = #{userId}
        </if>
    </select>
</mapper>