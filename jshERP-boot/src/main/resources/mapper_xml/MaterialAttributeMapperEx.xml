<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.MaterialAttributeMapperEx">

    <select id="selectByConditionMaterialAttribute" resultType="com.jsh.erp.datasource.entities.MaterialAttribute">
        select * from jsh_material_attribute ma
        where 1=1
        <if test="attributeName != null">
            <bind name="bindAttributeName" value="'%'+attributeName+'%'"/>
            and ma.attribute_name like #{bindAttributeName}
        </if>
        <if test="attributeValue != null">
            <bind name="bindAttributeValue" value="'%'+attributeValue+'%'"/>
            and ma.attribute_value like #{bindAttributeValue}
        </if>
        and ifnull(ma.delete_flag,'0') !='1'
        order by ma.id desc
    </select>

    <update id="batchDeleteMaterialAttributeByIds">
        update jsh_material_attribute
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
</mapper>