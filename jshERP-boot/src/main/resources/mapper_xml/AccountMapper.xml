<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.AccountMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.Account">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="initial_amount" jdbcType="DECIMAL" property="initialAmount" />
    <result column="current_amount" jdbcType="DECIMAL" property="currentAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="sort" jdbcType="VARCHAR" property="sort" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, serial_no, initial_amount, current_amount, remark, enabled, sort, is_default, 
    tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.AccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.AccountExample">
    delete from jsh_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.Account">
    insert into jsh_account (id, name, serial_no, 
      initial_amount, current_amount, remark, 
      enabled, sort, is_default, tenant_id, 
      delete_flag)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}, 
      #{initialAmount,jdbcType=DECIMAL}, #{currentAmount,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{enabled,jdbcType=BIT}, #{sort,jdbcType=VARCHAR}, #{isDefault,jdbcType=BIT}, #{tenantId,jdbcType=BIGINT}, 
      #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.Account">
    insert into jsh_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="initialAmount != null">
        initial_amount,
      </if>
      <if test="currentAmount != null">
        current_amount,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="initialAmount != null">
        #{initialAmount,jdbcType=DECIMAL},
      </if>
      <if test="currentAmount != null">
        #{currentAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.AccountExample" resultType="java.lang.Long">
    select count(*) from jsh_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.serialNo != null">
        serial_no = #{record.serialNo,jdbcType=VARCHAR},
      </if>
      <if test="record.initialAmount != null">
        initial_amount = #{record.initialAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.currentAmount != null">
        current_amount = #{record.currentAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=VARCHAR},
      </if>
      <if test="record.isDefault != null">
        is_default = #{record.isDefault,jdbcType=BIT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_account
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      serial_no = #{record.serialNo,jdbcType=VARCHAR},
      initial_amount = #{record.initialAmount,jdbcType=DECIMAL},
      current_amount = #{record.currentAmount,jdbcType=DECIMAL},
      remark = #{record.remark,jdbcType=VARCHAR},
      enabled = #{record.enabled,jdbcType=BIT},
      sort = #{record.sort,jdbcType=VARCHAR},
      is_default = #{record.isDefault,jdbcType=BIT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.Account">
    update jsh_account
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="initialAmount != null">
        initial_amount = #{initialAmount,jdbcType=DECIMAL},
      </if>
      <if test="currentAmount != null">
        current_amount = #{currentAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.Account">
    update jsh_account
    set name = #{name,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      initial_amount = #{initialAmount,jdbcType=DECIMAL},
      current_amount = #{currentAmount,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=BIT},
      sort = #{sort,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=BIT},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>