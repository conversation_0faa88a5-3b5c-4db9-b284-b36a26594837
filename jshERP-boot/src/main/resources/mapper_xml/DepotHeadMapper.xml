<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.DepotHeadMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.DepotHead">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="sub_type" jdbcType="VARCHAR" property="subType" />
    <result column="default_number" jdbcType="VARCHAR" property="defaultNumber" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="oper_time" jdbcType="TIMESTAMP" property="operTime" />
    <result column="organ_id" jdbcType="BIGINT" property="organId" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="change_amount" jdbcType="DECIMAL" property="changeAmount" />
    <result column="back_amount" jdbcType="DECIMAL" property="backAmount" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="bill_type" jdbcType="VARCHAR" property="billType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="sales_man" jdbcType="VARCHAR" property="salesMan" />
    <result column="account_id_list" jdbcType="VARCHAR" property="accountIdList" />
    <result column="account_money_list" jdbcType="VARCHAR" property="accountMoneyList" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="discount_money" jdbcType="DECIMAL" property="discountMoney" />
    <result column="discount_last_money" jdbcType="DECIMAL" property="discountLastMoney" />
    <result column="other_money" jdbcType="DECIMAL" property="otherMoney" />
    <result column="deposit" jdbcType="DECIMAL" property="deposit" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="purchase_status" jdbcType="VARCHAR" property="purchaseStatus" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="link_number" jdbcType="VARCHAR" property="linkNumber" />
    <result column="link_apply" jdbcType="VARCHAR" property="linkApply" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, sub_type, default_number, number, create_time, oper_time, organ_id, creator, 
    account_id, change_amount, back_amount, total_price, pay_type, bill_type, remark, 
    file_name, sales_man, account_id_list, account_money_list, discount, discount_money, 
    discount_last_money, other_money, deposit, status, purchase_status, source, link_number, 
    link_apply, tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.DepotHeadExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_depot_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_depot_head
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_depot_head
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.DepotHeadExample">
    delete from jsh_depot_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.DepotHead">
    insert into jsh_depot_head (id, type, sub_type, 
      default_number, number, create_time, 
      oper_time, organ_id, creator, 
      account_id, change_amount, back_amount, 
      total_price, pay_type, bill_type, 
      remark, file_name, sales_man, 
      account_id_list, account_money_list, discount, 
      discount_money, discount_last_money, other_money, 
      deposit, status, purchase_status, 
      source, link_number, link_apply, 
      tenant_id, delete_flag)
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{subType,jdbcType=VARCHAR}, 
      #{defaultNumber,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{operTime,jdbcType=TIMESTAMP}, #{organId,jdbcType=BIGINT}, #{creator,jdbcType=BIGINT}, 
      #{accountId,jdbcType=BIGINT}, #{changeAmount,jdbcType=DECIMAL}, #{backAmount,jdbcType=DECIMAL}, 
      #{totalPrice,jdbcType=DECIMAL}, #{payType,jdbcType=VARCHAR}, #{billType,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{salesMan,jdbcType=VARCHAR}, 
      #{accountIdList,jdbcType=VARCHAR}, #{accountMoneyList,jdbcType=VARCHAR}, #{discount,jdbcType=DECIMAL}, 
      #{discountMoney,jdbcType=DECIMAL}, #{discountLastMoney,jdbcType=DECIMAL}, #{otherMoney,jdbcType=DECIMAL}, 
      #{deposit,jdbcType=DECIMAL}, #{status,jdbcType=VARCHAR}, #{purchaseStatus,jdbcType=VARCHAR}, 
      #{source,jdbcType=VARCHAR}, #{linkNumber,jdbcType=VARCHAR}, #{linkApply,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.DepotHead">
    insert into jsh_depot_head
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="subType != null">
        sub_type,
      </if>
      <if test="defaultNumber != null">
        default_number,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="operTime != null">
        oper_time,
      </if>
      <if test="organId != null">
        organ_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="changeAmount != null">
        change_amount,
      </if>
      <if test="backAmount != null">
        back_amount,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="salesMan != null">
        sales_man,
      </if>
      <if test="accountIdList != null">
        account_id_list,
      </if>
      <if test="accountMoneyList != null">
        account_money_list,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="discountMoney != null">
        discount_money,
      </if>
      <if test="discountLastMoney != null">
        discount_last_money,
      </if>
      <if test="otherMoney != null">
        other_money,
      </if>
      <if test="deposit != null">
        deposit,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="purchaseStatus != null">
        purchase_status,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="linkNumber != null">
        link_number,
      </if>
      <if test="linkApply != null">
        link_apply,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=VARCHAR},
      </if>
      <if test="defaultNumber != null">
        #{defaultNumber,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operTime != null">
        #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="organId != null">
        #{organId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="changeAmount != null">
        #{changeAmount,jdbcType=DECIMAL},
      </if>
      <if test="backAmount != null">
        #{backAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="salesMan != null">
        #{salesMan,jdbcType=VARCHAR},
      </if>
      <if test="accountIdList != null">
        #{accountIdList,jdbcType=VARCHAR},
      </if>
      <if test="accountMoneyList != null">
        #{accountMoneyList,jdbcType=VARCHAR},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="discountMoney != null">
        #{discountMoney,jdbcType=DECIMAL},
      </if>
      <if test="discountLastMoney != null">
        #{discountLastMoney,jdbcType=DECIMAL},
      </if>
      <if test="otherMoney != null">
        #{otherMoney,jdbcType=DECIMAL},
      </if>
      <if test="deposit != null">
        #{deposit,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="purchaseStatus != null">
        #{purchaseStatus,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="linkNumber != null">
        #{linkNumber,jdbcType=VARCHAR},
      </if>
      <if test="linkApply != null">
        #{linkApply,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.DepotHeadExample" resultType="java.lang.Long">
    select count(*) from jsh_depot_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_depot_head
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.subType != null">
        sub_type = #{record.subType,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultNumber != null">
        default_number = #{record.defaultNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operTime != null">
        oper_time = #{record.operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.organId != null">
        organ_id = #{record.organId,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.changeAmount != null">
        change_amount = #{record.changeAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.backAmount != null">
        back_amount = #{record.backAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=VARCHAR},
      </if>
      <if test="record.billType != null">
        bill_type = #{record.billType,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.salesMan != null">
        sales_man = #{record.salesMan,jdbcType=VARCHAR},
      </if>
      <if test="record.accountIdList != null">
        account_id_list = #{record.accountIdList,jdbcType=VARCHAR},
      </if>
      <if test="record.accountMoneyList != null">
        account_money_list = #{record.accountMoneyList,jdbcType=VARCHAR},
      </if>
      <if test="record.discount != null">
        discount = #{record.discount,jdbcType=DECIMAL},
      </if>
      <if test="record.discountMoney != null">
        discount_money = #{record.discountMoney,jdbcType=DECIMAL},
      </if>
      <if test="record.discountLastMoney != null">
        discount_last_money = #{record.discountLastMoney,jdbcType=DECIMAL},
      </if>
      <if test="record.otherMoney != null">
        other_money = #{record.otherMoney,jdbcType=DECIMAL},
      </if>
      <if test="record.deposit != null">
        deposit = #{record.deposit,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseStatus != null">
        purchase_status = #{record.purchaseStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.linkNumber != null">
        link_number = #{record.linkNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.linkApply != null">
        link_apply = #{record.linkApply,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_depot_head
    set id = #{record.id,jdbcType=BIGINT},
      type = #{record.type,jdbcType=VARCHAR},
      sub_type = #{record.subType,jdbcType=VARCHAR},
      default_number = #{record.defaultNumber,jdbcType=VARCHAR},
      number = #{record.number,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      oper_time = #{record.operTime,jdbcType=TIMESTAMP},
      organ_id = #{record.organId,jdbcType=BIGINT},
      creator = #{record.creator,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=BIGINT},
      change_amount = #{record.changeAmount,jdbcType=DECIMAL},
      back_amount = #{record.backAmount,jdbcType=DECIMAL},
      total_price = #{record.totalPrice,jdbcType=DECIMAL},
      pay_type = #{record.payType,jdbcType=VARCHAR},
      bill_type = #{record.billType,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      sales_man = #{record.salesMan,jdbcType=VARCHAR},
      account_id_list = #{record.accountIdList,jdbcType=VARCHAR},
      account_money_list = #{record.accountMoneyList,jdbcType=VARCHAR},
      discount = #{record.discount,jdbcType=DECIMAL},
      discount_money = #{record.discountMoney,jdbcType=DECIMAL},
      discount_last_money = #{record.discountLastMoney,jdbcType=DECIMAL},
      other_money = #{record.otherMoney,jdbcType=DECIMAL},
      deposit = #{record.deposit,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=VARCHAR},
      purchase_status = #{record.purchaseStatus,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=VARCHAR},
      link_number = #{record.linkNumber,jdbcType=VARCHAR},
      link_apply = #{record.linkApply,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.DepotHead">
    update jsh_depot_head
    <set>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        sub_type = #{subType,jdbcType=VARCHAR},
      </if>
      <if test="defaultNumber != null">
        default_number = #{defaultNumber,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="organId != null">
        organ_id = #{organId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="changeAmount != null">
        change_amount = #{changeAmount,jdbcType=DECIMAL},
      </if>
      <if test="backAmount != null">
        back_amount = #{backAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="salesMan != null">
        sales_man = #{salesMan,jdbcType=VARCHAR},
      </if>
      <if test="accountIdList != null">
        account_id_list = #{accountIdList,jdbcType=VARCHAR},
      </if>
      <if test="accountMoneyList != null">
        account_money_list = #{accountMoneyList,jdbcType=VARCHAR},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="discountMoney != null">
        discount_money = #{discountMoney,jdbcType=DECIMAL},
      </if>
      <if test="discountLastMoney != null">
        discount_last_money = #{discountLastMoney,jdbcType=DECIMAL},
      </if>
      <if test="otherMoney != null">
        other_money = #{otherMoney,jdbcType=DECIMAL},
      </if>
      <if test="deposit != null">
        deposit = #{deposit,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="purchaseStatus != null">
        purchase_status = #{purchaseStatus,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="linkNumber != null">
        link_number = #{linkNumber,jdbcType=VARCHAR},
      </if>
      <if test="linkApply != null">
        link_apply = #{linkApply,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.DepotHead">
    update jsh_depot_head
    set type = #{type,jdbcType=VARCHAR},
      sub_type = #{subType,jdbcType=VARCHAR},
      default_number = #{defaultNumber,jdbcType=VARCHAR},
      number = #{number,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      oper_time = #{operTime,jdbcType=TIMESTAMP},
      organ_id = #{organId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      change_amount = #{changeAmount,jdbcType=DECIMAL},
      back_amount = #{backAmount,jdbcType=DECIMAL},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      pay_type = #{payType,jdbcType=VARCHAR},
      bill_type = #{billType,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      sales_man = #{salesMan,jdbcType=VARCHAR},
      account_id_list = #{accountIdList,jdbcType=VARCHAR},
      account_money_list = #{accountMoneyList,jdbcType=VARCHAR},
      discount = #{discount,jdbcType=DECIMAL},
      discount_money = #{discountMoney,jdbcType=DECIMAL},
      discount_last_money = #{discountLastMoney,jdbcType=DECIMAL},
      other_money = #{otherMoney,jdbcType=DECIMAL},
      deposit = #{deposit,jdbcType=DECIMAL},
      status = #{status,jdbcType=VARCHAR},
      purchase_status = #{purchaseStatus,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      link_number = #{linkNumber,jdbcType=VARCHAR},
      link_apply = #{linkApply,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>