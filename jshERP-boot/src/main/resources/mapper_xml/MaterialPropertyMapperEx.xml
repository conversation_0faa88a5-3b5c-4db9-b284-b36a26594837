<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.MaterialPropertyMapperEx">
    <select id="selectByConditionMaterialProperty" parameterType="com.jsh.erp.datasource.entities.MaterialPropertyExample" resultMap="com.jsh.erp.datasource.mappers.MaterialPropertyMapper.BaseResultMap">
        select *
        FROM jsh_material_property
        where 1=1
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and native_name like #{bindName}
        </if>
        and ifnull(delete_flag,'0') !='1'
        order by native_name asc
    </select>

    <update id="batchDeleteMaterialPropertyByIds">
        update jsh_material_property
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="getCountByNativeName" resultType="java.lang.Integer">
        select count(1)
        from jsh_material_property
        where native_name = #{nativeName}
        and ifnull(delete_flag,'0') !='1'
    </select>

    <update id="updateMaterialPropertyByNativeName">
        update jsh_material_property
        set another_name = #{anotherName}
        where native_name = #{nativeName}
        and ifnull(delete_flag,'0') !='1'
    </update>
</mapper>