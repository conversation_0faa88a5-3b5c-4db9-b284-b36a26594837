<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.RoleMapperEx">
    <resultMap extends="com.jsh.erp.datasource.mappers.RoleMapper.BaseResultMap" id="ResultExMap" type="com.jsh.erp.datasource.entities.RoleEx">
    </resultMap>
    <select id="selectByConditionRole" parameterType="com.jsh.erp.datasource.entities.RoleExample" resultMap="ResultExMap">
        SELECT *
        FROM jsh_role
        WHERE 1=1
        and ifnull(delete_flag,'0') !='1'
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and name like #{bindName}
        </if>
        <if test="description != null">
            <bind name="bindDescription" value="'%'+description+'%'"/>
            and description like #{bindDescription}
        </if>
        order by sort asc, id desc
    </select>

    <update id="batchDeleteRoleByIds">
        update jsh_role
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
    <select id="getRoleWithoutTenant" resultType="com.jsh.erp.datasource.entities.Role">
        select * from jsh_role
        where 1=1
        and ifnull(delete_flag,'0') !='1'
        and id=#{roleId}
    </select>
</mapper>