<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.AccountItemMapperEx">

    <resultMap extends="com.jsh.erp.datasource.mappers.AccountItemMapper.BaseResultMap" id="ResultWithInfoExMap" type="com.jsh.erp.datasource.vo.AccountItemVo4List">
        <result column="AccountName" jdbcType="VARCHAR" property="accountName" />
        <result column="InOutItemName" jdbcType="VARCHAR" property="inOutItemName" />
        <result column="billNumber" jdbcType="VARCHAR" property="billNumber" />
    </resultMap>

    <select id="selectByConditionAccountItem" parameterType="com.jsh.erp.datasource.entities.AccountItemExample" resultMap="com.jsh.erp.datasource.mappers.AccountItemMapper.BaseResultMap">
        select *
        FROM jsh_account_item
        where 1=1
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and name like #{bindName}
        </if>
        <if test="type != null">
            and type=#{type}
        </if>
        <if test="remark != null">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and remark like #{bindRemark}
        </if>
        and ifnull(delete_flag,'0') !='1'
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>
    <select id="countsByAccountItem" resultType="java.lang.Long">
        SELECT
        COUNT(id)
        FROM jsh_account_item
        WHERE 1=1
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and name like #{bindName}
        </if>
        <if test="type != null">
            and type=#{type}
        </if>
        <if test="remark != null">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and remark like #{bindRemark}
        </if>
        and ifnull(delete_flag,'0') !='1'
    </select>

    <select id="getDetailList" parameterType="com.jsh.erp.datasource.entities.AccountItemExample" resultMap="ResultWithInfoExMap">
        select ai.*,a.Name AccountName,ioi.Name InOutItemName, dh.number billNumber
        from jsh_account_item ai left join jsh_account a on ai.account_id=a.id and ifnull(a.delete_Flag,'0') !='1'
        left join jsh_in_out_item ioi on ai.in_out_item_id = ioi.id and ifnull(ioi.delete_Flag,'0') !='1'
        left join jsh_depot_head dh on ai.bill_id = dh.id and ifnull(dh.delete_Flag,'0') !='1'
        where ai.header_id = #{headerId}
        and ifnull(ai.delete_flag,'0') !='1'
        order by ai.id asc
    </select>
    <update id="batchDeleteAccountItemByIds">
        update jsh_account_item
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
    <select id="getAccountItemListByAccountIds" resultMap="com.jsh.erp.datasource.mappers.AccountItemMapper.BaseResultMap">
        select
        <include refid="com.jsh.erp.datasource.mappers.AccountItemMapper.Base_Column_List" />
        from jsh_account_item
        where 1=1
        and account_id in (
        <foreach collection="accountIds" item="accountId" separator=",">
            #{accountId}
        </foreach>
        )
        and ifnull(delete_flag,'0') !='1'
    </select>
    <select id="getAccountItemListByHeaderIds" resultMap="com.jsh.erp.datasource.mappers.AccountItemMapper.BaseResultMap">
        select
        <include refid="com.jsh.erp.datasource.mappers.AccountItemMapper.Base_Column_List" />
        from jsh_account_item
        where 1=1
        and header_id in (
        <foreach collection="headerIds" item="headerId" separator=",">
            #{headerId}
        </foreach>
        )
        and ifnull(delete_flag,'0') !='1'
    </select>
    <select id="getAccountItemListByInOutItemIds" resultMap="com.jsh.erp.datasource.mappers.AccountItemMapper.BaseResultMap">
        select
        <include refid="com.jsh.erp.datasource.mappers.AccountItemMapper.Base_Column_List" />
        from jsh_account_item
        where 1=1
        and in_out_item_id in (
        <foreach collection="inOutItemIds" item="inOutItemId" separator=",">
            #{inOutItemId}
        </foreach>
        )
        and ifnull(delete_flag,'0') !='1'
    </select>

    <update id="batchDeleteAccountItemByHeadIds">
        update jsh_account_item
        set delete_flag='1'
        where 1=1
        and header_id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="getEachAmountByBillId" resultType="java.math.BigDecimal">
        select
        ifnull(sum(each_amount),0)
        from jsh_account_item
        where 1=1
        <if test="billId != null">
            and bill_id=#{billId}
        </if>
        and ifnull(delete_flag,'0') !='1'
    </select>

    <select id="getFinishDebtByOrganId" resultType="java.math.BigDecimal">
        select ifnull(sum(ai.each_amount),0) from jsh_account_head ah
        left join jsh_account_item ai on ah.id = ai.header_id and ifnull(ai.delete_flag,'0') !='1'
        where ah.organ_id = #{organId}
        and (ah.type = '收款' or ah.type = '付款')
        and ai.bill_id is null
        and ifnull(ah.delete_flag,'0') !='1'
    </select>
</mapper>