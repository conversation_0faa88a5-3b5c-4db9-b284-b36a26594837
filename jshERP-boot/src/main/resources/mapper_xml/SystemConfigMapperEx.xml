<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.SystemConfigMapperEx">
    <select id="selectByConditionSystemConfig" parameterType="com.jsh.erp.datasource.entities.SystemConfigExample" resultMap="com.jsh.erp.datasource.mappers.SystemConfigMapper.BaseResultMap">
        select *
        FROM jsh_system_config
        where 1=1
        <if test="companyName != null">
            <bind name="bindCompanyName" value="'%'+companyName+'%'"/>
            and company_name like #{bindCompanyName}
        </if>
        and ifnull(delete_flag,'0') !='1'
    </select>

    <update id="batchDeleteSystemConfigByIds">
        update jsh_system_config
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
</mapper>