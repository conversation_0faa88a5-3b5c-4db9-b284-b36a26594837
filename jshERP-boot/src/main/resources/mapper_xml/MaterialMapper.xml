<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.MaterialMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.Material">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mfrs" jdbcType="VARCHAR" property="mfrs" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="standard" jdbcType="VARCHAR" property="standard" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="mnemonic" jdbcType="VARCHAR" property="mnemonic" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="img_name" jdbcType="VARCHAR" property="imgName" />
    <result column="unit_id" jdbcType="BIGINT" property="unitId" />
    <result column="expiry_num" jdbcType="INTEGER" property="expiryNum" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="other_field1" jdbcType="VARCHAR" property="otherField1" />
    <result column="other_field2" jdbcType="VARCHAR" property="otherField2" />
    <result column="other_field3" jdbcType="VARCHAR" property="otherField3" />
    <result column="enable_serial_number" jdbcType="VARCHAR" property="enableSerialNumber" />
    <result column="enable_batch_number" jdbcType="VARCHAR" property="enableBatchNumber" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="attribute" jdbcType="VARCHAR" property="attribute" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, category_id, name, mfrs, model, standard, brand, mnemonic, color, unit, remark, 
    img_name, unit_id, expiry_num, weight, enabled, other_field1, other_field2, other_field3, 
    enable_serial_number, enable_batch_number, position, attribute, tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.MaterialExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_material
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_material
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.MaterialExample">
    delete from jsh_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.Material">
    insert into jsh_material (id, category_id, name, 
      mfrs, model, standard, 
      brand, mnemonic, color, 
      unit, remark, img_name, 
      unit_id, expiry_num, weight, 
      enabled, other_field1, other_field2, 
      other_field3, enable_serial_number, enable_batch_number, 
      position, attribute, tenant_id, 
      delete_flag)
    values (#{id,jdbcType=BIGINT}, #{categoryId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{mfrs,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{standard,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{mnemonic,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{imgName,jdbcType=VARCHAR}, 
      #{unitId,jdbcType=BIGINT}, #{expiryNum,jdbcType=INTEGER}, #{weight,jdbcType=DECIMAL}, 
      #{enabled,jdbcType=BIT}, #{otherField1,jdbcType=VARCHAR}, #{otherField2,jdbcType=VARCHAR}, 
      #{otherField3,jdbcType=VARCHAR}, #{enableSerialNumber,jdbcType=VARCHAR}, #{enableBatchNumber,jdbcType=VARCHAR}, 
      #{position,jdbcType=VARCHAR}, #{attribute,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, 
      #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.Material">
    insert into jsh_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="mfrs != null">
        mfrs,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="standard != null">
        standard,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="mnemonic != null">
        mnemonic,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="imgName != null">
        img_name,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="expiryNum != null">
        expiry_num,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="otherField1 != null">
        other_field1,
      </if>
      <if test="otherField2 != null">
        other_field2,
      </if>
      <if test="otherField3 != null">
        other_field3,
      </if>
      <if test="enableSerialNumber != null">
        enable_serial_number,
      </if>
      <if test="enableBatchNumber != null">
        enable_batch_number,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="attribute != null">
        attribute,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mfrs != null">
        #{mfrs,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="standard != null">
        #{standard,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="mnemonic != null">
        #{mnemonic,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="imgName != null">
        #{imgName,jdbcType=VARCHAR},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=BIGINT},
      </if>
      <if test="expiryNum != null">
        #{expiryNum,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DECIMAL},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="otherField1 != null">
        #{otherField1,jdbcType=VARCHAR},
      </if>
      <if test="otherField2 != null">
        #{otherField2,jdbcType=VARCHAR},
      </if>
      <if test="otherField3 != null">
        #{otherField3,jdbcType=VARCHAR},
      </if>
      <if test="enableSerialNumber != null">
        #{enableSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="enableBatchNumber != null">
        #{enableBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="attribute != null">
        #{attribute,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.MaterialExample" resultType="java.lang.Long">
    select count(*) from jsh_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_material
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.mfrs != null">
        mfrs = #{record.mfrs,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.standard != null">
        standard = #{record.standard,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.mnemonic != null">
        mnemonic = #{record.mnemonic,jdbcType=VARCHAR},
      </if>
      <if test="record.color != null">
        color = #{record.color,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.imgName != null">
        img_name = #{record.imgName,jdbcType=VARCHAR},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=BIGINT},
      </if>
      <if test="record.expiryNum != null">
        expiry_num = #{record.expiryNum,jdbcType=INTEGER},
      </if>
      <if test="record.weight != null">
        weight = #{record.weight,jdbcType=DECIMAL},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.otherField1 != null">
        other_field1 = #{record.otherField1,jdbcType=VARCHAR},
      </if>
      <if test="record.otherField2 != null">
        other_field2 = #{record.otherField2,jdbcType=VARCHAR},
      </if>
      <if test="record.otherField3 != null">
        other_field3 = #{record.otherField3,jdbcType=VARCHAR},
      </if>
      <if test="record.enableSerialNumber != null">
        enable_serial_number = #{record.enableSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.enableBatchNumber != null">
        enable_batch_number = #{record.enableBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.position != null">
        position = #{record.position,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute != null">
        attribute = #{record.attribute,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_material
    set id = #{record.id,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      mfrs = #{record.mfrs,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      standard = #{record.standard,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      mnemonic = #{record.mnemonic,jdbcType=VARCHAR},
      color = #{record.color,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      img_name = #{record.imgName,jdbcType=VARCHAR},
      unit_id = #{record.unitId,jdbcType=BIGINT},
      expiry_num = #{record.expiryNum,jdbcType=INTEGER},
      weight = #{record.weight,jdbcType=DECIMAL},
      enabled = #{record.enabled,jdbcType=BIT},
      other_field1 = #{record.otherField1,jdbcType=VARCHAR},
      other_field2 = #{record.otherField2,jdbcType=VARCHAR},
      other_field3 = #{record.otherField3,jdbcType=VARCHAR},
      enable_serial_number = #{record.enableSerialNumber,jdbcType=VARCHAR},
      enable_batch_number = #{record.enableBatchNumber,jdbcType=VARCHAR},
      position = #{record.position,jdbcType=VARCHAR},
      attribute = #{record.attribute,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.Material">
    update jsh_material
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mfrs != null">
        mfrs = #{mfrs,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="standard != null">
        standard = #{standard,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="mnemonic != null">
        mnemonic = #{mnemonic,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="imgName != null">
        img_name = #{imgName,jdbcType=VARCHAR},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=BIGINT},
      </if>
      <if test="expiryNum != null">
        expiry_num = #{expiryNum,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=DECIMAL},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="otherField1 != null">
        other_field1 = #{otherField1,jdbcType=VARCHAR},
      </if>
      <if test="otherField2 != null">
        other_field2 = #{otherField2,jdbcType=VARCHAR},
      </if>
      <if test="otherField3 != null">
        other_field3 = #{otherField3,jdbcType=VARCHAR},
      </if>
      <if test="enableSerialNumber != null">
        enable_serial_number = #{enableSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="enableBatchNumber != null">
        enable_batch_number = #{enableBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="attribute != null">
        attribute = #{attribute,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.Material">
    update jsh_material
    set category_id = #{categoryId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      mfrs = #{mfrs,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      standard = #{standard,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      mnemonic = #{mnemonic,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      img_name = #{imgName,jdbcType=VARCHAR},
      unit_id = #{unitId,jdbcType=BIGINT},
      expiry_num = #{expiryNum,jdbcType=INTEGER},
      weight = #{weight,jdbcType=DECIMAL},
      enabled = #{enabled,jdbcType=BIT},
      other_field1 = #{otherField1,jdbcType=VARCHAR},
      other_field2 = #{otherField2,jdbcType=VARCHAR},
      other_field3 = #{otherField3,jdbcType=VARCHAR},
      enable_serial_number = #{enableSerialNumber,jdbcType=VARCHAR},
      enable_batch_number = #{enableBatchNumber,jdbcType=VARCHAR},
      position = #{position,jdbcType=VARCHAR},
      attribute = #{attribute,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>