<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.DepotHeadMapperEx">

    <resultMap extends="com.jsh.erp.datasource.mappers.DepotHeadMapper.BaseResultMap" id="ResultMapEx" type="com.jsh.erp.datasource.vo.DepotHeadVo4List">
        <result column="depotHeadType" jdbcType="VARCHAR" property="depotHeadType" />
        <result column="OrganName" jdbcType="VARCHAR" property="organName" />
        <result column="userName" jdbcType="VARCHAR" property="userName" />
        <result column="AccountName" jdbcType="VARCHAR" property="accountName" />
        <result column="contacts" jdbcType="VARCHAR" property="contacts" />
        <result column="telephone" jdbcType="VARCHAR" property="telephone" />
        <result column="address" jdbcType="VARCHAR" property="address" />
    </resultMap>

    <resultMap id="ResultWithInfoExMap" type="com.jsh.erp.datasource.vo.DepotHeadVo4InDetail">
        <result column="number" jdbcType="VARCHAR" property="number" />
        <result column="barCode" jdbcType="VARCHAR" property="barCode" />
        <result column="mname" jdbcType="VARCHAR" property="mname" />
        <result column="model" jdbcType="VARCHAR" property="model" />
        <result column="standard" jdbcType="VARCHAR" property="standard" />
        <result column="color" jdbcType="VARCHAR" property="color" />
        <result column="brand" jdbcType="VARCHAR" property="brand" />
        <result column="mfrs" jdbcType="VARCHAR" property="mfrs" />
        <result column="unitPrice" jdbcType="DECIMAL" property="unitPrice" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="operNumber" jdbcType="DECIMAL" property="operNumber" />
        <result column="allPrice" jdbcType="DECIMAL" property="allPrice" />
        <result column="taxRate" jdbcType="DECIMAL" property="taxRate" />
        <result column="taxMoney" jdbcType="DECIMAL" property="taxMoney" />
        <result column="taxLastMoney" jdbcType="DECIMAL" property="taxLastMoney" />
        <result column="sname" jdbcType="VARCHAR" property="sname" />
        <result column="dname" jdbcType="VARCHAR" property="dname" />
        <result column="operTime" jdbcType="VARCHAR" property="operTime" />
        <result column="newType" jdbcType="VARCHAR" property="newType" />
    </resultMap>

    <resultMap id="ResultInOutMaterialCount" type="com.jsh.erp.datasource.vo.DepotHeadVo4InOutMCount">
        <result column="materialId" jdbcType="BIGINT" property="materialId" />
        <result column="barCode" jdbcType="VARCHAR" property="barCode" />
        <result column="mName" jdbcType="VARCHAR" property="mName" />
        <result column="model" jdbcType="VARCHAR" property="model" />
        <result column="standard" jdbcType="VARCHAR" property="standard" />
        <result column="color" jdbcType="VARCHAR" property="color" />
        <result column="brand" jdbcType="VARCHAR" property="brand" />
        <result column="mfrs" jdbcType="VARCHAR" property="mfrs" />
        <result column="categoryName" jdbcType="VARCHAR" property="categoryName" />
        <result column="materialUnit" jdbcType="VARCHAR" property="materialUnit" />
        <result column="numSum" jdbcType="DECIMAL" property="numSum" />
        <result column="priceSum" jdbcType="DECIMAL" property="priceSum" />
    </resultMap>

    <resultMap id="ResultStatementAccount" type="com.jsh.erp.datasource.vo.DepotHeadVo4StatementAccount">
        <result column="number" jdbcType="VARCHAR" property="number" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="discount_last_money" jdbcType="DECIMAL" property="discountLastMoney" />
        <result column="other_money" jdbcType="DECIMAL" property="otherMoney" />
        <result column="change_amount" jdbcType="DECIMAL" property="changeAmount" />
        <result column="supplierName" jdbcType="VARCHAR" property="supplierName" />
        <result column="oTime" jdbcType="VARCHAR" property="oTime" />
    </resultMap>

    <select id="selectByConditionDepotHead" parameterType="com.jsh.erp.datasource.entities.DepotHeadExample" resultMap="ResultMapEx">
        select jdh.*, s.supplier OrganName, u.username userName, a.name AccountName
        from (select dh.id
        from jsh_depot_head dh
        left join jsh_depot_item di on dh.id = di.header_id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on di.material_id = m.id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on di.material_extend_id = me.id and ifnull(me.delete_flag,'0') !='1'
        where 1=1
        <if test="type != null">
            and dh.type=#{type}
        </if>
        <if test="subType != null">
            and dh.sub_type=#{subType}
        </if>
        <if test="statusArray != null and statusArray !=''">
            and dh.status in (
            <foreach collection="statusArray" item="status" separator=",">
                #{status}
            </foreach>
            )
        </if>
        <if test="purchaseStatusArray != null and purchaseStatusArray !=''">
            and dh.purchase_status in (
            <foreach collection="purchaseStatusArray" item="purchaseStatus" separator=",">
                #{purchaseStatus}
            </foreach>
            )
        </if>
        <if test="number != null">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="linkApply != null">
            <bind name="bindLinkApply" value="'%'+linkApply+'%'"/>
            and dh.link_apply like #{bindLinkApply}
        </if>
        <if test="linkNumber != null">
            <bind name="bindLinkNumber" value="'%'+linkNumber+'%'"/>
            and dh.link_number like #{bindLinkNumber}
        </if>
        <if test="beginTime != null">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="organId != null">
            and dh.organ_id=#{organId}
        </if>
        <if test="organArray != null and organArray !=''">
            and dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            )
        </if>
        <if test="creator != null">
            and dh.creator=#{creator}
        </if>
        <if test="depotId != null">
            and di.depot_id=#{depotId}
        </if>
        <if test="depotArray != null and depotArray !=''">
            and di.depot_id in (
            <foreach collection="depotArray" item="depotId" separator=",">
                #{depotId}
            </foreach>
            )
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="accountId != null">
            <bind name="bindAccountId" value="'%'+accountId+'%'"/>
            and (dh.account_id=#{accountId} or dh.account_id_list like #{bindAccountId})
        </if>
        <if test="hasDebt != null and hasDebt ==1">
            and
            (ifnull(dh.discount_last_money,0)+ifnull(dh.other_money,0)-ifnull(dh.deposit,0)-abs(ifnull(dh.change_amount,0))>0)
        </if>
        <if test="hasDebt != null and hasDebt ==0">
            and
            (ifnull(dh.discount_last_money,0)+ifnull(dh.other_money,0)-ifnull(dh.deposit,0)-abs(ifnull(dh.change_amount,0))=0)
        </if>
        <if test="remark != null and remark !=''">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and (dh.remark like #{bindRemark} or di.remark like #{bindRemark})
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        group by dh.id
        order by dh.id desc) tb
        left join jsh_depot_head jdh on jdh.id=tb.id and ifnull(jdh.delete_flag,'0') !='1'
        left join jsh_supplier s on jdh.organ_id=s.id and ifnull(s.delete_flag,'0') !='1'
        left join jsh_user u on jdh.creator=u.id
        left join jsh_account a on jdh.account_id=a.id and ifnull(a.delete_flag,'0') !='1'
        order by jdh.id desc
    </select>

    <select id="findMaterialsListMapByHeaderIdList" resultType="com.jsh.erp.datasource.vo.MaterialsListVo">
        select jsh_depot_item.header_id, group_concat(concat(jsh_material.name,' ',ifnull(jsh_material.standard,''),' ',
            ifnull(jsh_material.model,''),' ',ifnull(jsh_material.color,''))) as materialsList
        from jsh_depot_item
        left join jsh_material on jsh_depot_item.material_id = jsh_material.Id and ifnull(jsh_material.delete_Flag,'0') !='1'
        where 1=1
        <if test="idList.size()>0">
            and jsh_depot_item.header_id in
            <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and ifnull(jsh_depot_item.delete_flag,'0') !='1'
        group by jsh_depot_item.header_id
    </select>

    <select id="getMaterialCountListByHeaderIdList" resultType="com.jsh.erp.datasource.vo.MaterialCountVo">
        select header_id, sum(oper_number) materialCount from jsh_depot_item
        where 1=1
        <if test="idList.size()>0">
            and header_id in
            <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and ifnull(delete_flag,'0') !='1'
        group by header_id
    </select>

    <select id="findInOutDetail" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultWithInfoExMap">
        select dh.number,me.bar_code barCode, m.name mname,m.model,m.standard,m.color,m.brand,m.mfrs,di.unit_price unitPrice,di.material_unit as mUnit, di.sku,
        di.oper_number operNumber,di.all_price allPrice, ifnull(di.tax_rate,0) taxRate, ifnull(di.tax_money,0) taxMoney, ifnull(di.tax_last_money,0) taxLastMoney,
        s.supplier sname,d.dname dname, date_format(dh.oper_time, '%Y-%m-%d') operTime, concat(dh.sub_type,dh.type) as newType,
        concat_ws('  ',dh.remark,di.remark) as newRemark
        from jsh_depot_head dh
        left join jsh_depot_item di on di.header_id=dh.id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_supplier s on s.id=dh.organ_id and ifnull(s.delete_Flag,'0') !='1'
        left join (select id,name as dName,delete_Flag from jsh_depot ) d on d.id=di.depot_id and ifnull(d.delete_Flag,'0') !='1'
        where dh.oper_time >=#{beginTime} and dh.oper_time &lt;=#{endTime}
        <if test="oId != null">
            and dh.organ_id = #{oId}
        </if>
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="type != null">
            and dh.type=#{type}
        </if>
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organArray != null and organArray !=''">
            and (dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            ) or dh.sub_type='采购退货' or dh.sub_type='零售' or dh.sub_type='其它')
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="creator != null">
            and dh.creator=#{creator}
        </if>
        <if test="remark != null and remark !=''">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and (dh.remark like #{bindRemark} or di.remark like #{bindRemark})
        </if>
        and dh.sub_type!='调拨'
        and ifnull(dh.delete_flag,'0') !='1'
        <if test="column == 'createTime'">
            order by oper_time desc,number desc
        </if>
        <if test="column != 'createTime'">
            order by ${column} ${order}
        </if>
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="findInOutDetailCount" resultType="java.lang.Integer">
        select count(1)
        from jsh_depot_head dh
        left join jsh_depot_item di on di.header_id=dh.id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_Flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_supplier s on s.id=dh.organ_id and ifnull(s.delete_Flag,'0') !='1'
        left join (select id,name as dName,delete_Flag from jsh_depot) d on d.id=di.depot_id and ifnull(d.delete_Flag,'0') !='1'
        where dh.oper_time >=#{beginTime} and dh.oper_time &lt;=#{endTime}
        <if test="oId != null">
            and dh.organ_id = #{oId}
        </if>
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="type != null">
            and dh.type=#{type}
        </if>
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organArray != null and organArray !=''">
            and (dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            ) or dh.sub_type='采购退货' or dh.sub_type='零售' or dh.sub_type='其它')
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="creator != null">
            and dh.creator=#{creator}
        </if>
        <if test="remark != null and remark !=''">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and (dh.remark like #{bindRemark} or di.remark like #{bindRemark})
        </if>
        and dh.sub_type!='调拨'
        and ifnull(dh.delete_flag,'0') !='1'
    </select>

    <select id="findInOutDetailStatistic" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultWithInfoExMap">
        select ifnull(sum(di.oper_number),0) operNumber, ifnull(sum(di.all_price),0) allPrice
        from jsh_depot_head dh
        left join jsh_depot_item di on di.header_id=dh.id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_supplier s on s.id=dh.organ_id and ifnull(s.delete_Flag,'0') !='1'
        left join (select id,name as dName,delete_Flag from jsh_depot ) d on d.id=di.depot_id and ifnull(d.delete_Flag,'0') !='1'
        where dh.oper_time >=#{beginTime} and dh.oper_time &lt;=#{endTime}
        <if test="oId != null">
            and dh.organ_id = #{oId}
        </if>
        <if test="depotList.size()>0">
            and di.depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="type != null">
            and dh.type=#{type}
        </if>
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organArray != null and organArray !=''">
            and (dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            ) or dh.sub_type='采购退货' or dh.sub_type='零售' or dh.sub_type='其它')
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="creator != null">
            and dh.creator=#{creator}
        </if>
        <if test="remark != null and remark !=''">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and (dh.remark like #{bindRemark} or di.remark like #{bindRemark})
        </if>
        and dh.sub_type!='调拨'
        and ifnull(dh.delete_flag,'0') !='1'
    </select>

    <select id="findInOutMaterialCount" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultInOutMaterialCount">
        select di.material_id materialId, me.bar_code barCode, m.name mName,m.model,m.standard,m.color,m.brand,m.mfrs,
               mc.name categoryName,concat_ws('', m.unit, u.basic_unit) materialUnit,
        sum(di.basic_number) numSum,
        sum(di.all_price) priceSum
        from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_unit u on m.unit_id=u.id and ifnull(u.delete_Flag,'0') !='1'
        left join jsh_material_extend me on me.material_id=m.id and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_material_category mc on m.category_id=mc.Id and ifnull(mc.delete_flag,'0') !='1'
        where me.default_flag=1
        and (dh.type=#{type} or dh.sub_type='调拨') and dh.oper_time >=#{beginTime} and dh.oper_time &lt;=#{endTime}
        <if test="oId != null">
            and dh.organ_id = #{oId}
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organArray != null and organArray !=''">
            and (dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            ) or dh.sub_type='采购退货' or dh.sub_type='零售' or dh.sub_type='其它')
        </if>
        <if test="depotList.size()>0">
            <if test="type == '入库'">
                and ((
                dh.sub_type!='调拨' and di.depot_id in
                <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
                ) or (
                dh.sub_type='调拨' and di.another_depot_id in
                <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
                ))
            </if>
            <if test="type == '出库'">
                and di.depot_id in
                <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        group by di.material_id, me.bar_code, m.name,m.model,m.standard,m.color,m.brand,m.mfrs,
        mc.name, m.unit, u.basic_unit
        <if test="column == 'createTime'">
            order by materialId desc
        </if>
        <if test="column != 'createTime'">
            order by ${column} ${order}
        </if>
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="findInOutMaterialCountTotal" resultType="java.lang.Integer">
        select count(1) from
        (select di.material_id from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id  and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.material_id=m.id and ifnull(me.delete_flag,'0') !='1'
        where me.default_flag=1
        and (dh.type=#{type} or dh.sub_type='调拨') and dh.oper_time >=#{beginTime} and dh.oper_time &lt;=#{endTime}
        <if test="oId != null">
            and dh.organ_id = #{oId}
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organArray != null and organArray !=''">
            and (dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            ) or dh.sub_type='采购退货' or dh.sub_type='零售' or dh.sub_type='其它')
        </if>
        <if test="depotList.size()>0">
            <if test="type == '入库'">
                 and ((
                 dh.sub_type!='调拨' and di.depot_id in
                <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
                 ) or (
                 dh.sub_type='调拨' and di.another_depot_id in
                <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
                 ))
            </if>
            <if test="type == '出库'">
                and di.depot_id in
                <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        group by di.material_id) a
    </select>

    <select id="findInOutMaterialCountStatistic" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultInOutMaterialCount">
        select ifnull(sum(di.basic_number),0) numSum, ifnull(sum(di.all_price),0) priceSum
        from jsh_depot_head dh
        left join jsh_depot_item di on dh.id=di.header_id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_unit u on m.unit_id=u.id and ifnull(u.delete_Flag,'0') !='1'
        left join jsh_material_extend me on me.material_id=m.id and ifnull(me.delete_Flag,'0') !='1'
        left join jsh_material_category mc on m.category_id=mc.Id and ifnull(mc.delete_flag,'0') !='1'
        where me.default_flag=1
        and (dh.type=#{type} or dh.sub_type='调拨') and dh.oper_time >=#{beginTime} and dh.oper_time &lt;=#{endTime}
        <if test="oId != null">
            and dh.organ_id = #{oId}
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="organArray != null and organArray !=''">
            and (dh.organ_id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            ) or dh.sub_type='采购退货' or dh.sub_type='零售' or dh.sub_type='其它')
        </if>
        <if test="depotList.size()>0">
            <if test="type == '入库'">
                and ((
                dh.sub_type!='调拨' and di.depot_id in
                <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
                ) or (
                dh.sub_type='调拨' and di.another_depot_id in
                <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
                ))
            </if>
            <if test="type == '出库'">
                and di.depot_id in
                <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
        <if test="inOutManageFlag">
            and (dh.sub_type!='采购' and dh.sub_type!='采购退货' and dh.sub_type!='销售' and dh.sub_type!='销售退货')
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
    </select>

    <select id="findAllocationDetail" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultWithInfoExMap">
        select dh.number,me.bar_code barCode, m.name mname,m.model,m.standard,m.color,m.brand,m.mfrs,di.unit_price unitPrice,di.material_unit as mUnit,
               di.oper_number operNumber,di.all_price allPrice,sname, d.dName dname,date_format(dh.oper_time, '%Y-%m-%d') operTime,
               concat(dh.sub_type,dh.type) as newType, concat_ws('  ',dh.remark,di.remark) as newRemark
        from jsh_depot_head dh
        left join jsh_depot_item di on di.header_id=dh.id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id and ifnull(me.delete_Flag,'0') !='1'
        left join (select id,name as dName,delete_Flag from jsh_depot ) d on d.id=di.depot_id and ifnull(d.delete_Flag,'0') !='1'
        <!-- 调出仓库名查询 -->
        left join (select id as aid,name as sname,delete_Flag as adelete_Flag from jsh_depot ) ddd on ddd.aid=di.another_depot_id and ifnull(ddd.adelete_Flag,'0') !='1'
        where dh.oper_time >=#{beginTime} and dh.oper_time &lt;=#{endTime}
        <if test="depotFList.size()>0">
            and di.depot_id in
            <foreach collection="depotFList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="depotList.size()>0">
            and di.another_depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="subType != null">
            and dh.sub_type=#{subType}
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey} or dh.remark like #{bindKey})
        </if>
        <if test="remark != null and remark !=''">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and (dh.remark like #{bindRemark} or di.remark like #{bindRemark})
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        <if test="column == 'createTime'">
            order by oper_time desc,number desc
        </if>
        <if test="column != 'createTime'">
            order by ${column} ${order}
        </if>
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="findAllocationDetailCount" resultType="java.lang.Integer">
        select count(1)
        from jsh_depot_head dh
        left join jsh_depot_item di on di.header_id=dh.id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_Flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id and ifnull(me.delete_Flag,'0') !='1'
        left join (select id,name as dName,delete_Flag from jsh_depot) d on d.id=di.depot_id and ifnull(d.delete_Flag,'0') !='1'
        where dh.oper_time >=#{beginTime} and dh.oper_time &lt;=#{endTime}
        <if test="depotFList.size()>0">
            and di.depot_id in
            <foreach collection="depotFList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="depotList.size()>0">
            and di.another_depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="subType != null">
            and dh.sub_type=#{subType}
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey} or dh.remark like #{bindKey})
        </if>
        <if test="remark != null and remark !=''">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and (dh.remark like #{bindRemark} or di.remark like #{bindRemark})
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
    </select>

    <select id="findAllocationStatistic" parameterType="com.jsh.erp.datasource.entities.DepotItemExample" resultMap="ResultWithInfoExMap">
        select ifnull(sum(di.oper_number),0) operNumber, ifnull(sum(di.all_price),0) allPrice
        from jsh_depot_head dh
        left join jsh_depot_item di on di.header_id=dh.id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on m.id=di.material_id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on me.id=di.material_extend_id and ifnull(me.delete_Flag,'0') !='1'
        left join (select id,name as dName,delete_Flag from jsh_depot ) d on d.id=di.depot_id and ifnull(d.delete_Flag,'0') !='1'
        <!-- 调出仓库名查询 -->
        left join (select id as aid,name as sname,delete_Flag as adelete_Flag from jsh_depot ) ddd on ddd.aid=di.another_depot_id and ifnull(ddd.adelete_Flag,'0') !='1'
        where dh.oper_time >=#{beginTime} and dh.oper_time &lt;=#{endTime}
        <if test="depotFList.size()>0">
            and di.depot_id in
            <foreach collection="depotFList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="depotList.size()>0">
            and di.another_depot_id in
            <foreach collection="depotList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="subType != null">
            and dh.sub_type=#{subType}
        </if>
        <if test="number != null and number !=''">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        <if test="categoryList.size()>0">
            and m.category_id in
            <foreach collection="categoryList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="forceFlag">
            and dh.status = '1'
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.mnemonic like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.brand like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey} or dh.remark like #{bindKey})
        </if>
        <if test="remark != null and remark !=''">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and (dh.remark like #{bindRemark} or di.remark like #{bindRemark})
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
    </select>

    <select id="getStatementAccount" resultType="com.jsh.erp.datasource.vo.DepotHeadVo4StatementAccount">
        select *, (preBackInMoney+preBackOutMoney) as preBackMoney, (backInMoney+backOutMoney) as backMoney from
        (select s.id, s.supplier, s.contacts, s.telephone, s.phone_num, s.email,
            -- 起始期初金额
            (case when s.type='供应商' then ifnull(s.begin_need_pay,0) else ifnull(s.begin_need_get,0) end) begin_need,
            -- 上期欠款金额
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{type} and dh.sub_type=#{subType}
            and dh.oper_time &lt;= #{beginTime}
            and ifnull(dh.delete_flag,'0') !='1') preDebtMoney,
            -- 上期退货的欠款金额
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{typeBack} and dh.sub_type=#{subTypeBack}
            and dh.oper_time &lt;= #{beginTime}
            and ifnull(dh.delete_flag,'0') !='1') preReturnDebtMoney,
            -- 上期收款
            (select ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='收款'
            and ah.bill_time &lt;= #{beginTime}
            and ifnull(ai.delete_flag,'0') !='1') preBackInMoney,
            -- 上期付款
            (select 0-ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='付款'
            and ah.bill_time &lt;= #{beginTime}
            and ifnull(ai.delete_flag,'0') !='1') preBackOutMoney,
            -- 本期欠款
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{type} and dh.sub_type=#{subType}
            and dh.oper_time>#{beginTime} and dh.oper_time &lt;= #{endTime}
            and ifnull(dh.delete_flag,'0') !='1') debtMoney,
            -- 本期退货的欠款金额
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{typeBack} and dh.sub_type=#{subTypeBack}
            and dh.oper_time>#{beginTime} and dh.oper_time &lt;= #{endTime}
            and ifnull(dh.delete_flag,'0') !='1') returnDebtMoney,
            -- 本期收款
            (select ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='收款'
            and ah.bill_time>#{beginTime} and ah.bill_time &lt;= #{endTime}
            and ifnull(ai.delete_flag,'0') !='1') backInMoney,
            -- 本期付款
            (select 0-ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='付款'
            and ah.bill_time>#{beginTime} and ah.bill_time &lt;= #{endTime}
            and ifnull(ai.delete_flag,'0') !='1') backOutMoney
        from jsh_supplier s
        where s.enabled=1
        and s.type=#{supplierType}
        <if test="organId != null">
            and s.id = #{organId}
        </if>
        <if test="organArray != null and organArray !=''">
            and s.id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            )
        </if>
        and ifnull(s.delete_flag,'0') !='1') tb
        <if test="hasDebt == 1">
            where begin_need+preDebtMoney-preReturnDebtMoney-preBackInMoney-preBackOutMoney+debtMoney-returnDebtMoney-backInMoney-backOutMoney!=0
        </if>
        <if test="hasDebt == 0">
            where begin_need+preDebtMoney-preReturnDebtMoney-preBackInMoney-preBackOutMoney+debtMoney-returnDebtMoney-backInMoney-backOutMoney=0
        </if>
        order by begin_need+preDebtMoney-preReturnDebtMoney-preBackInMoney-preBackOutMoney+debtMoney-returnDebtMoney-backInMoney-backOutMoney desc
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="getStatementAccountCount" resultType="java.lang.Integer">
        select count(1) from
        (select s.id, s.supplier, s.contacts, s.telephone, s.phone_num, s.email,
            -- 起始期初金额
            (case when s.type='供应商' then ifnull(s.begin_need_pay,0) else ifnull(s.begin_need_get,0) end) begin_need,
            -- 上期欠款金额
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{type} and dh.sub_type=#{subType}
            and dh.oper_time &lt;= #{beginTime}
            and ifnull(dh.delete_flag,'0') !='1') preDebtMoney,
            -- 上期退货的欠款金额
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{typeBack} and dh.sub_type=#{subTypeBack}
            and dh.oper_time &lt;= #{beginTime}
            and ifnull(dh.delete_flag,'0') !='1') preReturnDebtMoney,
            -- 上期收款
            (select ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='收款'
            and ah.bill_time &lt;= #{beginTime}
            and ifnull(ai.delete_flag,'0') !='1') preBackInMoney,
            -- 上期付款
            (select 0-ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='付款'
            and ah.bill_time &lt;= #{beginTime}
            and ifnull(ai.delete_flag,'0') !='1') preBackOutMoney,
            -- 本期欠款
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{type} and dh.sub_type=#{subType}
            and dh.oper_time>#{beginTime} and dh.oper_time &lt;= #{endTime}
            and ifnull(dh.delete_flag,'0') !='1') debtMoney,
            -- 本期退货的欠款金额
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{typeBack} and dh.sub_type=#{subTypeBack}
            and dh.oper_time>#{beginTime} and dh.oper_time &lt;= #{endTime}
            and ifnull(dh.delete_flag,'0') !='1') returnDebtMoney,
            -- 本期收款
            (select ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='收款'
            and ah.bill_time>#{beginTime} and ah.bill_time &lt;= #{endTime}
            and ifnull(ai.delete_flag,'0') !='1') backInMoney,
            -- 本期付款
            (select 0-ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='付款'
            and ah.bill_time>#{beginTime} and ah.bill_time &lt;= #{endTime}
            and ifnull(ai.delete_flag,'0') !='1') backOutMoney
        from jsh_supplier s
        where s.enabled=1
        and s.type=#{supplierType}
        <if test="organId != null">
            and s.id = #{organId}
        </if>
        <if test="organArray != null and organArray !=''">
            and s.id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            )
        </if>
        and ifnull(s.delete_flag,'0') !='1') tb
        <if test="hasDebt == 1">
            where begin_need+preDebtMoney-preReturnDebtMoney-preBackInMoney-preBackOutMoney+debtMoney-returnDebtMoney-backInMoney-backOutMoney!=0
        </if>
        <if test="hasDebt == 0">
            where begin_need+preDebtMoney-preReturnDebtMoney-preBackInMoney-preBackOutMoney+debtMoney-returnDebtMoney-backInMoney-backOutMoney=0
        </if>
    </select>

    <select id="getStatementAccountTotalPay" resultType="com.jsh.erp.datasource.vo.DepotHeadVo4StatementAccount">
        select sum(begin_need) begin_need, sum(preDebtMoney) preDebtMoney, sum(preReturnDebtMoney) preReturnDebtMoney, sum(preBackInMoney+preBackOutMoney) preBackMoney,
               sum(debtMoney) debtMoney, sum(returnDebtMoney) returnDebtMoney, sum(backInMoney+backOutMoney) backMoney from
        (select s.id,
            (case when s.type='供应商' then ifnull(s.begin_need_pay,0) else ifnull(s.begin_need_get,0) end) begin_need,
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{type} and dh.sub_type=#{subType}
            and dh.oper_time &lt;= #{beginTime}
            and ifnull(dh.delete_flag,'0') !='1') preDebtMoney,
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{typeBack} and dh.sub_type=#{subTypeBack}
            and dh.oper_time &lt;= #{beginTime}
            and ifnull(dh.delete_flag,'0') !='1') preReturnDebtMoney,
            (select ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='收款'
            and ah.bill_time &lt;= #{beginTime}
            and ifnull(ai.delete_flag,'0') !='1') preBackInMoney,
            (select 0-ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='付款'
            and ah.bill_time &lt;= #{beginTime}
            and ifnull(ai.delete_flag,'0') !='1') preBackOutMoney,
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{type} and dh.sub_type=#{subType}
            and dh.oper_time>#{beginTime} and dh.oper_time &lt;= #{endTime}
            and ifnull(dh.delete_flag,'0') !='1') debtMoney,
            (select
            ifnull(sum(dh.discount_last_money),0)+ifnull(sum(dh.other_money),0)-ifnull(sum(dh.deposit),0)-abs(ifnull(sum(dh.change_amount),0))
            from jsh_depot_head dh
            where dh.organ_id=s.id
            and dh.type=#{typeBack} and dh.sub_type=#{subTypeBack}
            and dh.oper_time>#{beginTime} and dh.oper_time &lt;= #{endTime}
            and ifnull(dh.delete_flag,'0') !='1') returnDebtMoney,
            (select ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='收款'
            and ah.bill_time>#{beginTime} and ah.bill_time &lt;= #{endTime}
            and ifnull(ai.delete_flag,'0') !='1') backInMoney,
            (select 0-ifnull(sum(ai.each_amount),0) from jsh_account_item ai
            left join jsh_account_head ah on ai.header_id=ah.id and ifnull(ah.delete_flag,'0') !='1'
            where ah.organ_id=s.id and ah.type='付款'
            and ah.bill_time>#{beginTime} and ah.bill_time &lt;= #{endTime}
            and ifnull(ai.delete_flag,'0') !='1') backOutMoney
        from jsh_supplier s
        where s.enabled=1
        and s.type=#{supplierType}
        <if test="organId != null">
            and s.id = #{organId}
        </if>
        <if test="organArray != null and organArray !=''">
            and s.id in (
            <foreach collection="organArray" item="organId" separator=",">
                #{organId}
            </foreach>
            )
        </if>
        and ifnull(s.delete_flag,'0') !='1') tb
        <if test="hasDebt == 1">
            where begin_need+preDebtMoney-preReturnDebtMoney-preBackInMoney-preBackOutMoney+debtMoney-returnDebtMoney-backInMoney-backOutMoney!=0
        </if>
        <if test="hasDebt == 0">
            where begin_need+preDebtMoney-preReturnDebtMoney-preBackInMoney-preBackOutMoney+debtMoney-returnDebtMoney-backInMoney-backOutMoney=0
        </if>
    </select>

    <select id="getDetailByNumber" parameterType="com.jsh.erp.datasource.entities.DepotHeadExample" resultMap="ResultMapEx">
        select dh.*, concat(dh.sub_type,dh.type) as depotHeadType, a.name AccountName,
               s.supplier OrganName, s.contacts, s.telephone, s.address
        from jsh_depot_head dh
        left join jsh_supplier s on dh.organ_id=s.id and ifnull(s.delete_Flag,'0') !='1'
        left join jsh_account a on dh.account_id=a.id and ifnull(a.delete_Flag,'0') !='1'
        where 1=1
        <if test="number != null">
            and dh.number=#{number}
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
    </select>

    <update id="batchDeleteDepotHeadByIds">
        update jsh_depot_head
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="getDepotHeadListByAccountIds" resultMap="com.jsh.erp.datasource.mappers.DepotHeadMapper.BaseResultMap">
        select
        <include refid="com.jsh.erp.datasource.mappers.DepotHeadMapper.Base_Column_List" />
        from jsh_depot_head
        where 1=1
        and account_id in (
        <foreach collection="accountIds" item="accountId" separator=",">
            #{accountId}
        </foreach>
        )
        and ifnull(delete_flag,'0') !='1'
    </select>
    <select id="getDepotHeadListByOrganIds" resultMap="com.jsh.erp.datasource.mappers.DepotHeadMapper.BaseResultMap">
        select
        <include refid="com.jsh.erp.datasource.mappers.DepotHeadMapper.Base_Column_List" />
        from jsh_depot_head
        where 1=1
        and organ_id in (
        <foreach collection="organIds" item="organId" separator=",">
            #{organId}
        </foreach>
        )
        and ifnull(delete_flag,'0') !='1'
    </select>
    <select id="getDepotHeadListByCreator" resultMap="com.jsh.erp.datasource.mappers.DepotHeadMapper.BaseResultMap">
        select
        <include refid="com.jsh.erp.datasource.mappers.DepotHeadMapper.Base_Column_List" />
        from jsh_depot_head
        where 1=1
        and creator in (
        <foreach collection="creatorArray" item="creator" separator=",">
            #{creator}
        </foreach>
        )
        and ifnull(delete_flag,'0') !='1'
    </select>

    <select id="getBuyAndSaleStatisticsList" resultType="com.jsh.erp.datasource.vo.InOutPriceVo">
        select distinct dh.id, dh.discount_last_money, dh.total_price, dh.type, dh.sub_type, dh.oper_time
        from jsh_depot_head dh
        left join jsh_depot_item di on dh.id = di.header_id and ifnull(di.delete_flag,'0') !='1'
        where di.id is not null
        and (dh.type='入库' or dh.type='出库')
        <if test="beginTime != null">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        <if test="forceFlag">
            and (dh.status = '1' or dh.status = '2' or dh.status = '3')
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
    </select>

    <select id="debtList" parameterType="com.jsh.erp.datasource.entities.DepotHeadExample" resultMap="ResultMapEx">
        select distinct dh.*, s.supplier OrganName, u.username userName
        from jsh_depot_head dh
        left join jsh_supplier s on dh.organ_id=s.id and ifnull(s.delete_Flag,'0') !='1'
        left join jsh_user u on dh.creator=u.id and ifnull(u.delete_flag,'0') !='1'
        left join jsh_depot_item di on dh.id = di.header_id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on di.material_id = m.id and ifnull(m.delete_flag,'0') !='1'
        where 1=1
        and (dh.type='入库' or dh.type='出库')
        and (dh.sub_type='采购' or dh.sub_type='销售' or dh.sub_type='采购退货' or dh.sub_type='销售退货')
        <if test="organId != null">
            and dh.organ_id = #{organId}
        </if>
        <if test="status != null">
            and dh.status =#{status}
        </if>
        <if test="number != null">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="beginTime != null">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (m.name like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey})
        </if>
        <if test="depotArray != null and depotArray !=''">
            and di.depot_id in (
            <foreach collection="depotArray" item="depotId" separator=",">
                #{depotId}
            </foreach>
            )
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        and (ifnull(abs(dh.change_amount),0)+(select ifnull(sum(abs(ai.each_amount)),0) from jsh_account_item ai where ai.bill_id=dh.id and ifnull(ai.delete_flag,'0') !='1'))
        &lt; (ifnull(dh.discount_last_money,0) + ifnull(dh.other_money,0) - ifnull(dh.deposit,0))
        and ifnull(dh.delete_flag,'0') !='1'
        order by dh.oper_time desc
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>
    </select>

    <select id="debtListCount" resultType="java.lang.Integer">
        select count(1) from (
        select distinct dh.*, s.supplier OrganName, u.username userName
        from jsh_depot_head dh
        left join jsh_supplier s on dh.organ_id=s.id and ifnull(s.delete_Flag,'0') !='1'
        left join jsh_user u on dh.creator=u.id and ifnull(u.delete_flag,'0') !='1'
        left join jsh_depot_item di on dh.id = di.header_id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on di.material_id = m.id and ifnull(m.delete_flag,'0') !='1'
        where 1=1
        and (dh.type='入库' or dh.type='出库')
        and (dh.sub_type='采购' or dh.sub_type='销售' or dh.sub_type='采购退货' or dh.sub_type='销售退货')
        <if test="organId != null">
            and dh.organ_id = #{organId}
        </if>
        <if test="status != null">
            and dh.status =#{status}
        </if>
        <if test="number != null">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="beginTime != null">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (m.name like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey})
        </if>
        <if test="depotArray != null and depotArray !=''">
            and di.depot_id in (
            <foreach collection="depotArray" item="depotId" separator=",">
                #{depotId}
            </foreach>
            )
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        and (ifnull(abs(dh.change_amount),0)+(select ifnull(sum(abs(ai.each_amount)),0) from jsh_account_item ai where ai.bill_id=dh.id and ifnull(ai.delete_flag,'0') !='1'))
        &lt; (ifnull(dh.discount_last_money,0) + ifnull(dh.other_money,0) - ifnull(dh.deposit,0))
        and ifnull(dh.delete_flag,'0') !='1' ) tb
    </select>

    <select id="getFinishDepositByNumberList" resultType="com.jsh.erp.datasource.vo.FinishDepositVo">
        select dh.link_number number, ifnull(sum(dh.deposit),0) finishDeposit
        from jsh_depot_head dh
        where 1=1
        <if test="numberList.size()>0">
            and dh.link_number in
            <foreach collection="numberList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        group by dh.link_number
    </select>

    <select id="getFinishDepositByNumberExceptCurrent" resultType="java.math.BigDecimal">
        select ifnull(sum(dh.deposit),0)
        from jsh_depot_head dh
        where dh.link_number=#{linkNumber}
        and dh.number!=#{number}
        and ifnull(dh.delete_flag,'0') !='1'
    </select>

    <update id="setAccountIdToNull">
        update jsh_depot_head dh set dh.account_id = null
        where dh.id=#{id}
        and ifnull(dh.delete_flag,'0') !='1'
    </update>

    <select id="getSerialNumberBySell" resultType="java.lang.Integer">
        select count(s.id) from jsh_depot_head dh
        left join jsh_serial_number s on dh.number=s.in_bill_no
        where dh.number = #{number}
        and (s.out_bill_no is not null or s.out_bill_no!='')
    </select>

    <select id="waitBillList" parameterType="com.jsh.erp.datasource.entities.DepotHeadExample" resultMap="ResultMapEx">
        select jdh.*, s.supplier OrganName, u.username userName, a.name AccountName
        from (select dh.id
        from jsh_depot_head dh
        left join jsh_depot_item di on dh.id = di.header_id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on di.material_id = m.id and ifnull(m.delete_flag,'0') !='1'
        left join jsh_material_extend me on di.material_extend_id = me.id and ifnull(me.delete_flag,'0') !='1'
        where 1=1
        <if test="type != null">
            and dh.type=#{type}
        </if>
        <if test="subTypeArray != null and subTypeArray !=''">
            and dh.sub_type in (
            <foreach collection="subTypeArray" item="subType" separator=",">
                #{subType}
            </foreach>
            )
        </if>
        <if test="statusArray != null and statusArray !=''">
            and dh.status in (
            <foreach collection="statusArray" item="status" separator=",">
                #{status}
            </foreach>
            )
        </if>
        <if test="number != null">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="beginTime != null">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.standard like #{bindKey} or m.model like
            #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="depotArray != null and depotArray !=''">
            and di.depot_id in (
            <foreach collection="depotArray" item="depotId" separator=",">
                #{depotId}
            </foreach>
            )
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        and ifnull(dh.delete_flag,'0') !='1'
        group by dh.id
        order by dh.id desc
        <if test="offset != null and rows != null">
            limit #{offset},#{rows}
        </if>) tb
        left join jsh_depot_head jdh on jdh.id=tb.id and ifnull(jdh.delete_flag,'0') !='1'
        left join jsh_supplier s on jdh.organ_id=s.id and ifnull(s.delete_flag,'0') !='1'
        left join jsh_user u on jdh.creator=u.id and ifnull(u.delete_flag,'0') !='1'
        left join jsh_account a on jdh.account_id=a.id and ifnull(a.delete_flag,'0') !='1'
    </select>

    <select id="waitBillCount" resultType="java.lang.Long">
        select
        count(1) from
        (select distinct dh.id from jsh_depot_head dh
        left join jsh_depot_item di on dh.Id = di.header_id and ifnull(di.delete_flag,'0') !='1'
        left join jsh_material m on di.material_id = m.Id and ifnull(m.delete_Flag,'0') !='1'
        left join jsh_material_extend me on di.material_extend_id = me.id and ifnull(me.delete_flag,'0') !='1'
        WHERE 1=1
        <if test="type != null">
            and dh.type=#{type}
        </if>
        <if test="subTypeArray != null and subTypeArray !=''">
            and dh.sub_type in (
            <foreach collection="subTypeArray" item="subType" separator=",">
                #{subType}
            </foreach>
            )
        </if>
        <if test="statusArray != null and statusArray !=''">
            and dh.status in (
            <foreach collection="statusArray" item="status" separator=",">
                #{status}
            </foreach>
            )
        </if>
        <if test="number != null">
            <bind name="bindNumber" value="'%'+number+'%'"/>
            and dh.number like #{bindNumber}
        </if>
        <if test="beginTime != null">
            and dh.oper_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and dh.oper_time &lt;= #{endTime}
        </if>
        <if test="materialParam != null and materialParam !=''">
            <bind name="bindKey" value="'%'+materialParam+'%'"/>
            and (me.bar_code like #{bindKey} or m.name like #{bindKey} or m.standard like #{bindKey} or m.model like #{bindKey}
            or m.color like #{bindKey} or m.mfrs like #{bindKey} or m.other_field1 like #{bindKey}
            or m.other_field2 like #{bindKey} or m.other_field3 like #{bindKey})
        </if>
        <if test="depotArray != null and depotArray !=''">
            and di.depot_id in (
            <foreach collection="depotArray" item="depotId" separator=",">
                #{depotId}
            </foreach>
            )
        </if>
        <if test="creatorArray != null">
            and dh.creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        and ifnull(dh.delete_Flag,'0') !='1') tb
    </select>

    <select id="getBillAllPriceByOrganId" resultType="java.math.BigDecimal">
        select ifnull(sum(dh.total_price),0) allPrice from jsh_depot_head dh
        where dh.organ_id=#{organId}
        and dh.type = '出库'
        and dh.sub_type = '零售'
        and dh.pay_type = '预付款'
        and ifnull(dh.delete_flag,'0') !='1'
    </select>
</mapper>