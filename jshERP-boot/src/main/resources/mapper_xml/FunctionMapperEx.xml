<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.FunctionMapperEx">
    <resultMap extends="com.jsh.erp.datasource.mappers.FunctionMapper.BaseResultMap" id="ResultMapEx" type="com.jsh.erp.datasource.entities.FunctionEx">
        <result column="parent_name" jdbcType="VARCHAR" property="parentName" />
    </resultMap>
    <select id="selectByConditionFunction" parameterType="com.jsh.erp.datasource.entities.FunctionExample"
            resultMap="ResultMapEx">
        select fa.*, fb.name parent_name
        from jsh_function fa
        left join jsh_function fb on fa.parent_number = fb.number
        where 1=1
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and fa.name like #{bindName}
        </if>
        <if test="type != null">
            and fa.type=#{type}
        </if>
        and ifnull(fa.delete_flag,'0') !='1'
        order by fa.sort asc
    </select>

    <update id="batchDeleteFunctionByIds">
        update jsh_function
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
</mapper>