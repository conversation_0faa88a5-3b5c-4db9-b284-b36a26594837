<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.SupplierMapperEx">
    <select id="selectByConditionSupplier" parameterType="com.jsh.erp.datasource.entities.SupplierExample" resultMap="com.jsh.erp.datasource.mappers.SupplierMapper.BaseResultMap">
        select *
        FROM jsh_supplier
        where 1=1
        <if test="supplier != null and supplier !=''">
            <bind name="bindSupplier" value="'%'+supplier+'%'"/>
            and supplier like #{bindSupplier}
        </if>
        <if test="type != null and type !=''">
            and type=#{type}
        </if>
        <if test="contacts != null and contacts !=''">
            <bind name="bindContacts" value="'%'+contacts+'%'"/>
            and contacts like #{bindContacts}
        </if>
        <if test="phonenum != null and phonenum !=''">
            <bind name="bindPhoneNum" value="'%'+phonenum+'%'"/>
            and phone_num like #{bindPhoneNum}
        </if>
        <if test="telephone != null and telephone !=''">
            <bind name="bindTelephone" value="'%'+telephone+'%'"/>
            and telephone like #{bindTelephone}
        </if>
        <if test="creatorArray != null">
            and creator in (
            <foreach collection="creatorArray" item="creator" separator=",">
                #{creator}
            </foreach>
            )
        </if>
        and ifnull(delete_flag,'0') !='1'
        order by sort asc, id desc
    </select>

    <select id="findByAll" parameterType="com.jsh.erp.datasource.entities.SupplierExample" resultMap="com.jsh.erp.datasource.mappers.SupplierMapper.BaseResultMap">
        select *
        FROM jsh_supplier
        where 1=1
        <if test="supplier != null and supplier !=''">
            <bind name="bindSupplier" value="'%'+supplier+'%'"/>
            and supplier like #{bindSupplier}
        </if>
        <if test="type != null and type !=''">
            and type=#{type}
        </if>
        <if test="phonenum != null and phonenum !=''">
            <bind name="bindPhoneNum" value="'%'+phonenum+'%'"/>
            and phone_num like #{bindPhoneNum}
        </if>
        <if test="telephone != null and telephone !=''">
            <bind name="bindTelephone" value="'%'+telephone+'%'"/>
            and telephone like #{bindTelephone}
        </if>
        and ifnull(delete_flag,'0') !='1'
        order by sort asc, id desc
    </select>

    <update id="batchDeleteSupplierByIds">
        update jsh_supplier
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="getSupplierByNameAndType" resultType="com.jsh.erp.datasource.entities.Supplier">
        select *
        from jsh_supplier
        where supplier = #{supplier} and type = #{type}
        and ifnull(delete_flag,'0') !='1'
    </select>
</mapper>