<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.AccountItemMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.AccountItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="header_id" jdbcType="BIGINT" property="headerId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="in_out_item_id" jdbcType="BIGINT" property="inOutItemId" />
    <result column="bill_id" jdbcType="BIGINT" property="billId" />
    <result column="need_debt" jdbcType="DECIMAL" property="needDebt" />
    <result column="finish_debt" jdbcType="DECIMAL" property="finishDebt" />
    <result column="each_amount" jdbcType="DECIMAL" property="eachAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, header_id, account_id, in_out_item_id, bill_id, need_debt, finish_debt, each_amount, 
    remark, tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.AccountItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_account_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_account_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_account_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.AccountItemExample">
    delete from jsh_account_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.AccountItem">
    insert into jsh_account_item (id, header_id, account_id, 
      in_out_item_id, bill_id, need_debt, 
      finish_debt, each_amount, remark, 
      tenant_id, delete_flag)
    values (#{id,jdbcType=BIGINT}, #{headerId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, 
      #{inOutItemId,jdbcType=BIGINT}, #{billId,jdbcType=BIGINT}, #{needDebt,jdbcType=DECIMAL}, 
      #{finishDebt,jdbcType=DECIMAL}, #{eachAmount,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.AccountItem">
    insert into jsh_account_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headerId != null">
        header_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="inOutItemId != null">
        in_out_item_id,
      </if>
      <if test="billId != null">
        bill_id,
      </if>
      <if test="needDebt != null">
        need_debt,
      </if>
      <if test="finishDebt != null">
        finish_debt,
      </if>
      <if test="eachAmount != null">
        each_amount,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="headerId != null">
        #{headerId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="inOutItemId != null">
        #{inOutItemId,jdbcType=BIGINT},
      </if>
      <if test="billId != null">
        #{billId,jdbcType=BIGINT},
      </if>
      <if test="needDebt != null">
        #{needDebt,jdbcType=DECIMAL},
      </if>
      <if test="finishDebt != null">
        #{finishDebt,jdbcType=DECIMAL},
      </if>
      <if test="eachAmount != null">
        #{eachAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.AccountItemExample" resultType="java.lang.Long">
    select count(*) from jsh_account_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_account_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.headerId != null">
        header_id = #{record.headerId,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.inOutItemId != null">
        in_out_item_id = #{record.inOutItemId,jdbcType=BIGINT},
      </if>
      <if test="record.billId != null">
        bill_id = #{record.billId,jdbcType=BIGINT},
      </if>
      <if test="record.needDebt != null">
        need_debt = #{record.needDebt,jdbcType=DECIMAL},
      </if>
      <if test="record.finishDebt != null">
        finish_debt = #{record.finishDebt,jdbcType=DECIMAL},
      </if>
      <if test="record.eachAmount != null">
        each_amount = #{record.eachAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_account_item
    set id = #{record.id,jdbcType=BIGINT},
      header_id = #{record.headerId,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=BIGINT},
      in_out_item_id = #{record.inOutItemId,jdbcType=BIGINT},
      bill_id = #{record.billId,jdbcType=BIGINT},
      need_debt = #{record.needDebt,jdbcType=DECIMAL},
      finish_debt = #{record.finishDebt,jdbcType=DECIMAL},
      each_amount = #{record.eachAmount,jdbcType=DECIMAL},
      remark = #{record.remark,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.AccountItem">
    update jsh_account_item
    <set>
      <if test="headerId != null">
        header_id = #{headerId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="inOutItemId != null">
        in_out_item_id = #{inOutItemId,jdbcType=BIGINT},
      </if>
      <if test="billId != null">
        bill_id = #{billId,jdbcType=BIGINT},
      </if>
      <if test="needDebt != null">
        need_debt = #{needDebt,jdbcType=DECIMAL},
      </if>
      <if test="finishDebt != null">
        finish_debt = #{finishDebt,jdbcType=DECIMAL},
      </if>
      <if test="eachAmount != null">
        each_amount = #{eachAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.AccountItem">
    update jsh_account_item
    set header_id = #{headerId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      in_out_item_id = #{inOutItemId,jdbcType=BIGINT},
      bill_id = #{billId,jdbcType=BIGINT},
      need_debt = #{needDebt,jdbcType=DECIMAL},
      finish_debt = #{finishDebt,jdbcType=DECIMAL},
      each_amount = #{eachAmount,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>