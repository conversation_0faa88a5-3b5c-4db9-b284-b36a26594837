<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.MaterialInitialStockMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.MaterialInitialStock">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="depot_id" jdbcType="BIGINT" property="depotId" />
    <result column="number" jdbcType="DECIMAL" property="number" />
    <result column="low_safe_stock" jdbcType="DECIMAL" property="lowSafeStock" />
    <result column="high_safe_stock" jdbcType="DECIMAL" property="highSafeStock" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, material_id, depot_id, number, low_safe_stock, high_safe_stock, tenant_id, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.MaterialInitialStockExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_material_initial_stock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_material_initial_stock
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_material_initial_stock
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.MaterialInitialStockExample">
    delete from jsh_material_initial_stock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.MaterialInitialStock">
    insert into jsh_material_initial_stock (id, material_id, depot_id, 
      number, low_safe_stock, high_safe_stock, 
      tenant_id, delete_flag)
    values (#{id,jdbcType=BIGINT}, #{materialId,jdbcType=BIGINT}, #{depotId,jdbcType=BIGINT}, 
      #{number,jdbcType=DECIMAL}, #{lowSafeStock,jdbcType=DECIMAL}, #{highSafeStock,jdbcType=DECIMAL}, 
      #{tenantId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.MaterialInitialStock">
    insert into jsh_material_initial_stock
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="depotId != null">
        depot_id,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="lowSafeStock != null">
        low_safe_stock,
      </if>
      <if test="highSafeStock != null">
        high_safe_stock,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="depotId != null">
        #{depotId,jdbcType=BIGINT},
      </if>
      <if test="number != null">
        #{number,jdbcType=DECIMAL},
      </if>
      <if test="lowSafeStock != null">
        #{lowSafeStock,jdbcType=DECIMAL},
      </if>
      <if test="highSafeStock != null">
        #{highSafeStock,jdbcType=DECIMAL},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.MaterialInitialStockExample" resultType="java.lang.Long">
    select count(*) from jsh_material_initial_stock
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_material_initial_stock
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=BIGINT},
      </if>
      <if test="record.depotId != null">
        depot_id = #{record.depotId,jdbcType=BIGINT},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=DECIMAL},
      </if>
      <if test="record.lowSafeStock != null">
        low_safe_stock = #{record.lowSafeStock,jdbcType=DECIMAL},
      </if>
      <if test="record.highSafeStock != null">
        high_safe_stock = #{record.highSafeStock,jdbcType=DECIMAL},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_material_initial_stock
    set id = #{record.id,jdbcType=BIGINT},
      material_id = #{record.materialId,jdbcType=BIGINT},
      depot_id = #{record.depotId,jdbcType=BIGINT},
      number = #{record.number,jdbcType=DECIMAL},
      low_safe_stock = #{record.lowSafeStock,jdbcType=DECIMAL},
      high_safe_stock = #{record.highSafeStock,jdbcType=DECIMAL},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.MaterialInitialStock">
    update jsh_material_initial_stock
    <set>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="depotId != null">
        depot_id = #{depotId,jdbcType=BIGINT},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=DECIMAL},
      </if>
      <if test="lowSafeStock != null">
        low_safe_stock = #{lowSafeStock,jdbcType=DECIMAL},
      </if>
      <if test="highSafeStock != null">
        high_safe_stock = #{highSafeStock,jdbcType=DECIMAL},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.MaterialInitialStock">
    update jsh_material_initial_stock
    set material_id = #{materialId,jdbcType=BIGINT},
      depot_id = #{depotId,jdbcType=BIGINT},
      number = #{number,jdbcType=DECIMAL},
      low_safe_stock = #{lowSafeStock,jdbcType=DECIMAL},
      high_safe_stock = #{highSafeStock,jdbcType=DECIMAL},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>