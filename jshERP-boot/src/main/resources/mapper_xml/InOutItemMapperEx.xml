<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.InOutItemMapperEx">
    <select id="selectByConditionInOutItem" parameterType="com.jsh.erp.datasource.entities.InOutItemExample" resultMap="com.jsh.erp.datasource.mappers.InOutItemMapper.BaseResultMap">
        select *
        FROM jsh_in_out_item
        where 1=1
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and name like #{bindName}
        </if>
        <if test="type != null">
            and type=#{type}
        </if>
        <if test="remark != null">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and remark like #{bindRemark}
        </if>
        and ifnull(delete_flag,'0') !='1'
        order by sort asc, id desc
    </select>

    <update id="batchDeleteInOutItemByIds">
        update jsh_in_out_item
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
</mapper>