<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.PersonMapperEx">
    <select id="selectByConditionPerson" parameterType="com.jsh.erp.datasource.entities.PersonExample" resultMap="com.jsh.erp.datasource.mappers.PersonMapper.BaseResultMap">
        select *
        FROM jsh_person
        where 1=1
        <if test="name != null and name != ''">
            <bind name="bindName" value="'%'+name+'%'"/>
            and name like #{bindName}
        </if>
        <if test="type != null and type != ''">
            and type=#{type}
        </if>
        and ifnull(delete_flag,'0') !='1'
        order by sort asc, id desc
    </select>

    <update id="batchDeletePersonByIds">
        update jsh_person
        set delete_flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
</mapper>