<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.TenantMapperEx">

    <resultMap extends="com.jsh.erp.datasource.mappers.LogMapper.BaseResultMap" id="ResultMapEx" type="com.jsh.erp.datasource.entities.TenantEx">
        <result column="roleId" jdbcType="VARCHAR" property="roleId" />
        <result column="roleName" jdbcType="VARCHAR" property="roleName" />
        <result column="userCount" jdbcType="VARCHAR" property="userCount" />
    </resultMap>

    <select id="selectByConditionTenant" parameterType="com.jsh.erp.datasource.entities.TenantExample" resultMap="ResultMapEx">
        select t.*,
        (select r.id from jsh_user_business ub
        left join jsh_role r on ub.value=concat("[",r.id,"]") and ifnull(r.delete_flag,'0') !='1'
        where ub.type='UserRole' and ub.key_id=t.tenant_id limit 0,1) roleId,
        (select r.name from jsh_user_business ub
        left join jsh_role r on ub.value=concat("[",r.id,"]") and ifnull(r.delete_flag,'0') !='1'
        where ub.type='UserRole' and ub.key_id=t.tenant_id limit 0,1) roleName,
        (select count(jsh_user.id) from jsh_user where jsh_user.status='0' and jsh_user.delete_flag=0 and jsh_user.tenant_id=t.tenant_id) userCount
        from jsh_tenant t
        where 1=1
        <if test="loginName != null">
            <bind name="bindLoginName" value="'%'+loginName+'%'"/>
            and t.login_name like #{bindLoginName}
        </if>
        <if test="type != null and type != ''">
            and t.type = #{type}
        </if>
        <if test="enabled != null and enabled != ''">
            and t.enabled = #{enabled}
        </if>
        <if test="remark != null">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and t.remark like #{bindRemark}
        </if>
        and ifnull(t.delete_flag,'0') !='1'
        order by t.id desc
    </select>

</mapper>