<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.DepotMapperEx">
    <resultMap extends="com.jsh.erp.datasource.mappers.DepotMapper.BaseResultMap" id="ResultMapEx" type="com.jsh.erp.datasource.entities.DepotEx">
        <result column="principalName" jdbcType="VARCHAR" property="principalName" />
    </resultMap>

    <select id="selectByConditionDepot" parameterType="com.jsh.erp.datasource.entities.DepotExample" resultMap="ResultMapEx">
        select dep.*,usr.username as principalName
        FROM jsh_depot dep
        left join jsh_user usr on usr.id=dep.principal and ifnull(usr.status,'0') not in('1','2')
        where 1=1
        <if test="name != null">
            <bind name="bindName" value="'%'+name+'%'"/>
            and dep.name like #{bindName}
        </if>
        <if test="type != null">
            and dep.type=#{type}
        </if>
        <if test="remark != null">
            <bind name="bindRemark" value="'%'+remark+'%'"/>
            and dep.remark like #{bindRemark}
        </if>
        and ifnull(dep.delete_Flag,'0') !='1'
        order by dep.sort asc, dep.id desc
    </select>

    <update id="batchDeleteDepotByIds">
        update jsh_depot
        set delete_Flag='1'
        where 1=1
        and id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>



</mapper>