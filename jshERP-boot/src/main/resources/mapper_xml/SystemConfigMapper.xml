<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.SystemConfigMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.SystemConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_contacts" jdbcType="VARCHAR" property="companyContacts" />
    <result column="company_address" jdbcType="VARCHAR" property="companyAddress" />
    <result column="company_tel" jdbcType="VARCHAR" property="companyTel" />
    <result column="company_fax" jdbcType="VARCHAR" property="companyFax" />
    <result column="company_post_code" jdbcType="VARCHAR" property="companyPostCode" />
    <result column="sale_agreement" jdbcType="VARCHAR" property="saleAgreement" />
    <result column="depot_flag" jdbcType="VARCHAR" property="depotFlag" />
    <result column="customer_flag" jdbcType="VARCHAR" property="customerFlag" />
    <result column="minus_stock_flag" jdbcType="VARCHAR" property="minusStockFlag" />
    <result column="purchase_by_sale_flag" jdbcType="VARCHAR" property="purchaseBySaleFlag" />
    <result column="multi_level_approval_flag" jdbcType="VARCHAR" property="multiLevelApprovalFlag" />
    <result column="multi_bill_type" jdbcType="VARCHAR" property="multiBillType" />
    <result column="force_approval_flag" jdbcType="VARCHAR" property="forceApprovalFlag" />
    <result column="update_unit_price_flag" jdbcType="VARCHAR" property="updateUnitPriceFlag" />
    <result column="over_link_bill_flag" jdbcType="VARCHAR" property="overLinkBillFlag" />
    <result column="in_out_manage_flag" jdbcType="VARCHAR" property="inOutManageFlag" />
    <result column="multi_account_flag" jdbcType="VARCHAR" property="multiAccountFlag" />
    <result column="move_avg_price_flag" jdbcType="VARCHAR" property="moveAvgPriceFlag" />
    <result column="audit_print_flag" jdbcType="VARCHAR" property="auditPrintFlag" />
    <result column="zero_change_amount_flag" jdbcType="VARCHAR" property="zeroChangeAmountFlag" />
    <result column="customer_static_price_flag" jdbcType="VARCHAR" property="customerStaticPriceFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, 
    sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, 
    multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, 
    over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, 
    audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, 
    delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.SystemConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_system_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_system_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_system_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.SystemConfigExample">
    delete from jsh_system_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.SystemConfig">
    insert into jsh_system_config (id, company_name, company_contacts, 
      company_address, company_tel, company_fax, 
      company_post_code, sale_agreement, depot_flag, 
      customer_flag, minus_stock_flag, purchase_by_sale_flag, 
      multi_level_approval_flag, multi_bill_type, 
      force_approval_flag, update_unit_price_flag, 
      over_link_bill_flag, in_out_manage_flag, multi_account_flag, 
      move_avg_price_flag, audit_print_flag, zero_change_amount_flag, 
      customer_static_price_flag, tenant_id, delete_flag
      )
    values (#{id,jdbcType=BIGINT}, #{companyName,jdbcType=VARCHAR}, #{companyContacts,jdbcType=VARCHAR}, 
      #{companyAddress,jdbcType=VARCHAR}, #{companyTel,jdbcType=VARCHAR}, #{companyFax,jdbcType=VARCHAR}, 
      #{companyPostCode,jdbcType=VARCHAR}, #{saleAgreement,jdbcType=VARCHAR}, #{depotFlag,jdbcType=VARCHAR}, 
      #{customerFlag,jdbcType=VARCHAR}, #{minusStockFlag,jdbcType=VARCHAR}, #{purchaseBySaleFlag,jdbcType=VARCHAR}, 
      #{multiLevelApprovalFlag,jdbcType=VARCHAR}, #{multiBillType,jdbcType=VARCHAR}, 
      #{forceApprovalFlag,jdbcType=VARCHAR}, #{updateUnitPriceFlag,jdbcType=VARCHAR}, 
      #{overLinkBillFlag,jdbcType=VARCHAR}, #{inOutManageFlag,jdbcType=VARCHAR}, #{multiAccountFlag,jdbcType=VARCHAR}, 
      #{moveAvgPriceFlag,jdbcType=VARCHAR}, #{auditPrintFlag,jdbcType=VARCHAR}, #{zeroChangeAmountFlag,jdbcType=VARCHAR}, 
      #{customerStaticPriceFlag,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.SystemConfig">
    insert into jsh_system_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="companyContacts != null">
        company_contacts,
      </if>
      <if test="companyAddress != null">
        company_address,
      </if>
      <if test="companyTel != null">
        company_tel,
      </if>
      <if test="companyFax != null">
        company_fax,
      </if>
      <if test="companyPostCode != null">
        company_post_code,
      </if>
      <if test="saleAgreement != null">
        sale_agreement,
      </if>
      <if test="depotFlag != null">
        depot_flag,
      </if>
      <if test="customerFlag != null">
        customer_flag,
      </if>
      <if test="minusStockFlag != null">
        minus_stock_flag,
      </if>
      <if test="purchaseBySaleFlag != null">
        purchase_by_sale_flag,
      </if>
      <if test="multiLevelApprovalFlag != null">
        multi_level_approval_flag,
      </if>
      <if test="multiBillType != null">
        multi_bill_type,
      </if>
      <if test="forceApprovalFlag != null">
        force_approval_flag,
      </if>
      <if test="updateUnitPriceFlag != null">
        update_unit_price_flag,
      </if>
      <if test="overLinkBillFlag != null">
        over_link_bill_flag,
      </if>
      <if test="inOutManageFlag != null">
        in_out_manage_flag,
      </if>
      <if test="multiAccountFlag != null">
        multi_account_flag,
      </if>
      <if test="moveAvgPriceFlag != null">
        move_avg_price_flag,
      </if>
      <if test="auditPrintFlag != null">
        audit_print_flag,
      </if>
      <if test="zeroChangeAmountFlag != null">
        zero_change_amount_flag,
      </if>
      <if test="customerStaticPriceFlag != null">
        customer_static_price_flag,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyContacts != null">
        #{companyContacts,jdbcType=VARCHAR},
      </if>
      <if test="companyAddress != null">
        #{companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyTel != null">
        #{companyTel,jdbcType=VARCHAR},
      </if>
      <if test="companyFax != null">
        #{companyFax,jdbcType=VARCHAR},
      </if>
      <if test="companyPostCode != null">
        #{companyPostCode,jdbcType=VARCHAR},
      </if>
      <if test="saleAgreement != null">
        #{saleAgreement,jdbcType=VARCHAR},
      </if>
      <if test="depotFlag != null">
        #{depotFlag,jdbcType=VARCHAR},
      </if>
      <if test="customerFlag != null">
        #{customerFlag,jdbcType=VARCHAR},
      </if>
      <if test="minusStockFlag != null">
        #{minusStockFlag,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBySaleFlag != null">
        #{purchaseBySaleFlag,jdbcType=VARCHAR},
      </if>
      <if test="multiLevelApprovalFlag != null">
        #{multiLevelApprovalFlag,jdbcType=VARCHAR},
      </if>
      <if test="multiBillType != null">
        #{multiBillType,jdbcType=VARCHAR},
      </if>
      <if test="forceApprovalFlag != null">
        #{forceApprovalFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateUnitPriceFlag != null">
        #{updateUnitPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="overLinkBillFlag != null">
        #{overLinkBillFlag,jdbcType=VARCHAR},
      </if>
      <if test="inOutManageFlag != null">
        #{inOutManageFlag,jdbcType=VARCHAR},
      </if>
      <if test="multiAccountFlag != null">
        #{multiAccountFlag,jdbcType=VARCHAR},
      </if>
      <if test="moveAvgPriceFlag != null">
        #{moveAvgPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="auditPrintFlag != null">
        #{auditPrintFlag,jdbcType=VARCHAR},
      </if>
      <if test="zeroChangeAmountFlag != null">
        #{zeroChangeAmountFlag,jdbcType=VARCHAR},
      </if>
      <if test="customerStaticPriceFlag != null">
        #{customerStaticPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.SystemConfigExample" resultType="java.lang.Long">
    select count(*) from jsh_system_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_system_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyContacts != null">
        company_contacts = #{record.companyContacts,jdbcType=VARCHAR},
      </if>
      <if test="record.companyAddress != null">
        company_address = #{record.companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.companyTel != null">
        company_tel = #{record.companyTel,jdbcType=VARCHAR},
      </if>
      <if test="record.companyFax != null">
        company_fax = #{record.companyFax,jdbcType=VARCHAR},
      </if>
      <if test="record.companyPostCode != null">
        company_post_code = #{record.companyPostCode,jdbcType=VARCHAR},
      </if>
      <if test="record.saleAgreement != null">
        sale_agreement = #{record.saleAgreement,jdbcType=VARCHAR},
      </if>
      <if test="record.depotFlag != null">
        depot_flag = #{record.depotFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.customerFlag != null">
        customer_flag = #{record.customerFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.minusStockFlag != null">
        minus_stock_flag = #{record.minusStockFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseBySaleFlag != null">
        purchase_by_sale_flag = #{record.purchaseBySaleFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.multiLevelApprovalFlag != null">
        multi_level_approval_flag = #{record.multiLevelApprovalFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.multiBillType != null">
        multi_bill_type = #{record.multiBillType,jdbcType=VARCHAR},
      </if>
      <if test="record.forceApprovalFlag != null">
        force_approval_flag = #{record.forceApprovalFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUnitPriceFlag != null">
        update_unit_price_flag = #{record.updateUnitPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.overLinkBillFlag != null">
        over_link_bill_flag = #{record.overLinkBillFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.inOutManageFlag != null">
        in_out_manage_flag = #{record.inOutManageFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.multiAccountFlag != null">
        multi_account_flag = #{record.multiAccountFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.moveAvgPriceFlag != null">
        move_avg_price_flag = #{record.moveAvgPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.auditPrintFlag != null">
        audit_print_flag = #{record.auditPrintFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.zeroChangeAmountFlag != null">
        zero_change_amount_flag = #{record.zeroChangeAmountFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.customerStaticPriceFlag != null">
        customer_static_price_flag = #{record.customerStaticPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_system_config
    set id = #{record.id,jdbcType=BIGINT},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      company_contacts = #{record.companyContacts,jdbcType=VARCHAR},
      company_address = #{record.companyAddress,jdbcType=VARCHAR},
      company_tel = #{record.companyTel,jdbcType=VARCHAR},
      company_fax = #{record.companyFax,jdbcType=VARCHAR},
      company_post_code = #{record.companyPostCode,jdbcType=VARCHAR},
      sale_agreement = #{record.saleAgreement,jdbcType=VARCHAR},
      depot_flag = #{record.depotFlag,jdbcType=VARCHAR},
      customer_flag = #{record.customerFlag,jdbcType=VARCHAR},
      minus_stock_flag = #{record.minusStockFlag,jdbcType=VARCHAR},
      purchase_by_sale_flag = #{record.purchaseBySaleFlag,jdbcType=VARCHAR},
      multi_level_approval_flag = #{record.multiLevelApprovalFlag,jdbcType=VARCHAR},
      multi_bill_type = #{record.multiBillType,jdbcType=VARCHAR},
      force_approval_flag = #{record.forceApprovalFlag,jdbcType=VARCHAR},
      update_unit_price_flag = #{record.updateUnitPriceFlag,jdbcType=VARCHAR},
      over_link_bill_flag = #{record.overLinkBillFlag,jdbcType=VARCHAR},
      in_out_manage_flag = #{record.inOutManageFlag,jdbcType=VARCHAR},
      multi_account_flag = #{record.multiAccountFlag,jdbcType=VARCHAR},
      move_avg_price_flag = #{record.moveAvgPriceFlag,jdbcType=VARCHAR},
      audit_print_flag = #{record.auditPrintFlag,jdbcType=VARCHAR},
      zero_change_amount_flag = #{record.zeroChangeAmountFlag,jdbcType=VARCHAR},
      customer_static_price_flag = #{record.customerStaticPriceFlag,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.SystemConfig">
    update jsh_system_config
    <set>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyContacts != null">
        company_contacts = #{companyContacts,jdbcType=VARCHAR},
      </if>
      <if test="companyAddress != null">
        company_address = #{companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyTel != null">
        company_tel = #{companyTel,jdbcType=VARCHAR},
      </if>
      <if test="companyFax != null">
        company_fax = #{companyFax,jdbcType=VARCHAR},
      </if>
      <if test="companyPostCode != null">
        company_post_code = #{companyPostCode,jdbcType=VARCHAR},
      </if>
      <if test="saleAgreement != null">
        sale_agreement = #{saleAgreement,jdbcType=VARCHAR},
      </if>
      <if test="depotFlag != null">
        depot_flag = #{depotFlag,jdbcType=VARCHAR},
      </if>
      <if test="customerFlag != null">
        customer_flag = #{customerFlag,jdbcType=VARCHAR},
      </if>
      <if test="minusStockFlag != null">
        minus_stock_flag = #{minusStockFlag,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBySaleFlag != null">
        purchase_by_sale_flag = #{purchaseBySaleFlag,jdbcType=VARCHAR},
      </if>
      <if test="multiLevelApprovalFlag != null">
        multi_level_approval_flag = #{multiLevelApprovalFlag,jdbcType=VARCHAR},
      </if>
      <if test="multiBillType != null">
        multi_bill_type = #{multiBillType,jdbcType=VARCHAR},
      </if>
      <if test="forceApprovalFlag != null">
        force_approval_flag = #{forceApprovalFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateUnitPriceFlag != null">
        update_unit_price_flag = #{updateUnitPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="overLinkBillFlag != null">
        over_link_bill_flag = #{overLinkBillFlag,jdbcType=VARCHAR},
      </if>
      <if test="inOutManageFlag != null">
        in_out_manage_flag = #{inOutManageFlag,jdbcType=VARCHAR},
      </if>
      <if test="multiAccountFlag != null">
        multi_account_flag = #{multiAccountFlag,jdbcType=VARCHAR},
      </if>
      <if test="moveAvgPriceFlag != null">
        move_avg_price_flag = #{moveAvgPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="auditPrintFlag != null">
        audit_print_flag = #{auditPrintFlag,jdbcType=VARCHAR},
      </if>
      <if test="zeroChangeAmountFlag != null">
        zero_change_amount_flag = #{zeroChangeAmountFlag,jdbcType=VARCHAR},
      </if>
      <if test="customerStaticPriceFlag != null">
        customer_static_price_flag = #{customerStaticPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.SystemConfig">
    update jsh_system_config
    set company_name = #{companyName,jdbcType=VARCHAR},
      company_contacts = #{companyContacts,jdbcType=VARCHAR},
      company_address = #{companyAddress,jdbcType=VARCHAR},
      company_tel = #{companyTel,jdbcType=VARCHAR},
      company_fax = #{companyFax,jdbcType=VARCHAR},
      company_post_code = #{companyPostCode,jdbcType=VARCHAR},
      sale_agreement = #{saleAgreement,jdbcType=VARCHAR},
      depot_flag = #{depotFlag,jdbcType=VARCHAR},
      customer_flag = #{customerFlag,jdbcType=VARCHAR},
      minus_stock_flag = #{minusStockFlag,jdbcType=VARCHAR},
      purchase_by_sale_flag = #{purchaseBySaleFlag,jdbcType=VARCHAR},
      multi_level_approval_flag = #{multiLevelApprovalFlag,jdbcType=VARCHAR},
      multi_bill_type = #{multiBillType,jdbcType=VARCHAR},
      force_approval_flag = #{forceApprovalFlag,jdbcType=VARCHAR},
      update_unit_price_flag = #{updateUnitPriceFlag,jdbcType=VARCHAR},
      over_link_bill_flag = #{overLinkBillFlag,jdbcType=VARCHAR},
      in_out_manage_flag = #{inOutManageFlag,jdbcType=VARCHAR},
      multi_account_flag = #{multiAccountFlag,jdbcType=VARCHAR},
      move_avg_price_flag = #{moveAvgPriceFlag,jdbcType=VARCHAR},
      audit_print_flag = #{auditPrintFlag,jdbcType=VARCHAR},
      zero_change_amount_flag = #{zeroChangeAmountFlag,jdbcType=VARCHAR},
      customer_static_price_flag = #{customerStaticPriceFlag,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>