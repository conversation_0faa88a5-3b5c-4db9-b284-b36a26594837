<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.SerialNumberMapper">
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.SerialNumber">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="depot_id" jdbcType="BIGINT" property="depotId" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="is_sell" jdbcType="VARCHAR" property="isSell" />
    <result column="in_price" jdbcType="DECIMAL" property="inPrice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
    <result column="in_bill_no" jdbcType="VARCHAR" property="inBillNo" />
    <result column="out_bill_no" jdbcType="VARCHAR" property="outBillNo" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, material_id, depot_id, serial_number, is_sell, in_price, remark, delete_flag, 
    create_time, creator, update_time, updater, in_bill_no, out_bill_no, tenant_id
  </sql>
  <select id="selectByExample" parameterType="com.jsh.erp.datasource.entities.SerialNumberExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jsh_serial_number
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_serial_number
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_serial_number
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsh.erp.datasource.entities.SerialNumberExample">
    delete from jsh_serial_number
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.SerialNumber">
    insert into jsh_serial_number (id, material_id, depot_id, 
      serial_number, is_sell, in_price, 
      remark, delete_flag, create_time, 
      creator, update_time, updater, 
      in_bill_no, out_bill_no, tenant_id
      )
    values (#{id,jdbcType=BIGINT}, #{materialId,jdbcType=BIGINT}, #{depotId,jdbcType=BIGINT}, 
      #{serialNumber,jdbcType=VARCHAR}, #{isSell,jdbcType=VARCHAR}, #{inPrice,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=BIGINT}, 
      #{inBillNo,jdbcType=VARCHAR}, #{outBillNo,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.SerialNumber">
    insert into jsh_serial_number
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="depotId != null">
        depot_id,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="isSell != null">
        is_sell,
      </if>
      <if test="inPrice != null">
        in_price,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="inBillNo != null">
        in_bill_no,
      </if>
      <if test="outBillNo != null">
        out_bill_no,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="depotId != null">
        #{depotId,jdbcType=BIGINT},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="isSell != null">
        #{isSell,jdbcType=VARCHAR},
      </if>
      <if test="inPrice != null">
        #{inPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=BIGINT},
      </if>
      <if test="inBillNo != null">
        #{inBillNo,jdbcType=VARCHAR},
      </if>
      <if test="outBillNo != null">
        #{outBillNo,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsh.erp.datasource.entities.SerialNumberExample" resultType="java.lang.Long">
    select count(*) from jsh_serial_number
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update jsh_serial_number
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=BIGINT},
      </if>
      <if test="record.depotId != null">
        depot_id = #{record.depotId,jdbcType=BIGINT},
      </if>
      <if test="record.serialNumber != null">
        serial_number = #{record.serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.isSell != null">
        is_sell = #{record.isSell,jdbcType=VARCHAR},
      </if>
      <if test="record.inPrice != null">
        in_price = #{record.inPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updater != null">
        updater = #{record.updater,jdbcType=BIGINT},
      </if>
      <if test="record.inBillNo != null">
        in_bill_no = #{record.inBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.outBillNo != null">
        out_bill_no = #{record.outBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update jsh_serial_number
    set id = #{record.id,jdbcType=BIGINT},
      material_id = #{record.materialId,jdbcType=BIGINT},
      depot_id = #{record.depotId,jdbcType=BIGINT},
      serial_number = #{record.serialNumber,jdbcType=VARCHAR},
      is_sell = #{record.isSell,jdbcType=VARCHAR},
      in_price = #{record.inPrice,jdbcType=DECIMAL},
      remark = #{record.remark,jdbcType=VARCHAR},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      updater = #{record.updater,jdbcType=BIGINT},
      in_bill_no = #{record.inBillNo,jdbcType=VARCHAR},
      out_bill_no = #{record.outBillNo,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.SerialNumber">
    update jsh_serial_number
    <set>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="depotId != null">
        depot_id = #{depotId,jdbcType=BIGINT},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="isSell != null">
        is_sell = #{isSell,jdbcType=VARCHAR},
      </if>
      <if test="inPrice != null">
        in_price = #{inPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=BIGINT},
      </if>
      <if test="inBillNo != null">
        in_bill_no = #{inBillNo,jdbcType=VARCHAR},
      </if>
      <if test="outBillNo != null">
        out_bill_no = #{outBillNo,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsh.erp.datasource.entities.SerialNumber">
    update jsh_serial_number
    set material_id = #{materialId,jdbcType=BIGINT},
      depot_id = #{depotId,jdbcType=BIGINT},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      is_sell = #{isSell,jdbcType=VARCHAR},
      in_price = #{inPrice,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=BIGINT},
      in_bill_no = #{inBillNo,jdbcType=VARCHAR},
      out_bill_no = #{outBillNo,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>