<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.LogMapperEx">
    <resultMap extends="com.jsh.erp.datasource.mappers.LogMapper.BaseResultMap" id="ResultExMap" type="com.jsh.erp.datasource.vo.LogVo4List">
        <result column="userName" jdbcType="VARCHAR" property="userName" />
        <result column="login_name" jdbcType="VARCHAR" property="loginName" />
    </resultMap>

    <select id="selectByConditionLog" parameterType="com.jsh.erp.datasource.entities.LogExample" resultMap="ResultExMap">
        select l.*,u.login_name,u.username userName
        FROM jsh_log l
        left join jsh_user u on l.user_id = u.id
        left join jsh_tenant t on l.tenant_id=t.tenant_id
        where 1=1
        <if test="operation != null">
            <bind name="bindOperation" value="'%'+operation+'%'"/>
            and l.operation like #{bindOperation}
        </if>
        <if test="userInfo != null">
            <bind name="bindUserInfo" value="'%'+userInfo+'%'"/>
            and (u.username like #{bindUserInfo} or u.login_name like #{bindUserInfo})
        </if>
        <if test="clientIp != null">
            <bind name="bindClientIp" value="'%'+clientIp+'%'"/>
            and l.client_ip like #{bindClientIp}
        </if>
        <if test="tenantLoginName != null">
            and t.login_name = #{tenantLoginName}
        </if>
        <if test="tenantType != null">
            and t.type = #{tenantType}
        </if>
        <if test="beginTime != null">
            and l.create_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and l.create_time &lt;= #{endTime}
        </if>
        <if test="content != null">
            <bind name="bindContent" value="'%'+content+'%'"/>
            and l.content like #{bindContent}
        </if>
        order by l.create_time desc
    </select>

    <select id="getCountByIpAndDate" resultType="java.lang.Long">
        select count(1) from jsh_log
        where user_id=#{userId} and operation=#{moduleName} and client_ip=#{clientIp} and create_time=#{createTime}
    </select>

    <insert id="insertLogWithUserId" parameterType="com.jsh.erp.datasource.entities.Log">
        insert into jsh_log (user_id, operation,
                             client_ip, create_time, status,
                             content, tenant_id)
        values (#{userId,jdbcType=BIGINT}, #{operation,jdbcType=VARCHAR},
                #{clientIp,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT},
                #{content,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT})
    </insert>
</mapper>