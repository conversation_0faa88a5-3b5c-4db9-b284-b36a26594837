server.port=9999
#????-?
server.servlet.session.timeout=36000
#????
server.servlet.context-path=/jshERP-boot
#?????
spring.datasource.url=***************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=98828007ac
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
#mybatis-plus??
mybatis-plus.mapper-locations=classpath:./mapper_xml/*.xml
# Redis
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=98828007ac
#???????id
manage.roleId=10
#??????????
tenant.userNumLimit=1000000
#?????????
tenant.tryDayLimit=3000
#????
plugin.runMode=prod
plugin.pluginPath=plugins
plugin.pluginConfigFilePath=pluginConfig
#?????? 1-?? 2-oss
file.uploadType=1
#???????
file.path=/opt/jshERP/upload
#????????
server.tomcat.basedir=/opt/tmp/tomcat
#??????(byte)
spring.servlet.multipart.max-file-size=10485760
spring.servlet.multipart.max-request-size=10485760