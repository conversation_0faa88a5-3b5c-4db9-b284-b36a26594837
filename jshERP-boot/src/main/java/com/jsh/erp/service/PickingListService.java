package com.jsh.erp.service;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.constants.BusinessConstants;
import com.jsh.erp.constants.ExceptionConstants;
import com.jsh.erp.datasource.entities.*;
import com.jsh.erp.datasource.mappers.DepotHeadMapper;
import com.jsh.erp.datasource.mappers.DepotItemMapper;
import com.jsh.erp.datasource.vo.DepotHeadVo4List;
import com.jsh.erp.exception.BusinessRunTimeException;
import com.jsh.erp.utils.PageUtils;
import com.jsh.erp.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 配货单服务类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-07
 */
@Service
public class PickingListService {
    
    private Logger logger = LoggerFactory.getLogger(PickingListService.class);
    
    @Resource
    private DepotHeadMapper depotHeadMapper;
    
    @Resource
    private DepotItemMapper depotItemMapper;
    
    @Resource
    private DepotHeadService depotHeadService;
    
    @Resource
    private DepotItemService depotItemService;
    
    @Resource
    private MaterialService materialService;
    
    @Resource
    private SupplierService supplierService;
    
    @Resource
    private PersonService personService;
    
    @Resource
    private UserService userService;
    
    @Resource
    private DepotService depotService;

    @Resource
    private SequenceService sequenceService;

    // 配货单类型常量
    private static final String PICKING_TYPE = "其它";
    private static final String PICKING_SUB_TYPE = "配货单";
    private static final String PICKING_PREFIX = "PHD";  // 配货单编号前缀

    // 配货状态常量
    public static final String STATUS_UNPICKED = "0";  // 未配货
    public static final String STATUS_PICKING = "1";   // 配货中
    public static final String STATUS_COMPLETED = "2"; // 已完成
    
    /**
     * 获取配货单列表（标准分页查询）
     */
    public List<DepotHeadVo4List> select(String number, String materialParam, Long organId,
                                        String linkNumber, String pickingStatus, Long creator,
                                        String beginTime, String endTime, String remark) throws Exception {
        List<DepotHeadVo4List> list = new ArrayList<>();
        try {
            // 启动分页
            PageUtils.startPage();

            // 调用DepotHeadService的select方法，只查询配货单
            // 参数顺序：type, subType, hasDebt, status, purchaseStatus, number, linkApply, linkNumber, beginTime, endTime, materialParam, organId, creator, depotId, accountId, remark
            list = depotHeadService.select(PICKING_TYPE, PICKING_SUB_TYPE, null, pickingStatus, null,
                number, null, linkNumber, beginTime, endTime, materialParam, organId, creator, null, null, remark);
        } catch (Exception e) {
            logger.error("查询配货单列表失败", e);
            throw new BusinessRunTimeException(ExceptionConstants.DATA_READ_FAIL_CODE,
                ExceptionConstants.DATA_READ_FAIL_MSG);
        }
        return list;
    }

    /**
     * 根据ID获取配货单详情
     */
    public List<DepotHeadVo4List> getDetailById(Long id, HttpServletRequest request) throws Exception {
        // 先获取配货单基本信息
        DepotHead depotHead = depotHeadMapper.selectByPrimaryKey(id);
        if (depotHead == null || !PICKING_TYPE.equals(depotHead.getType())
            || !PICKING_SUB_TYPE.equals(depotHead.getSubType())) {
            return new ArrayList<>();
        }

        // 通过编号查询详细信息
        return depotHeadService.getDetailByNumber(depotHead.getNumber(), request);
    }
    
    /**
     * 新增配货单
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int insertPickingListWithDetail(JSONObject obj, HttpServletRequest request) throws Exception {
        
        DepotHead depotHead = JSONObject.parseObject(obj.toJSONString(), DepotHead.class);
        
        // 设置配货单特有属性
        depotHead.setType(PICKING_TYPE);
        depotHead.setSubType(PICKING_SUB_TYPE);
        
        // 设置默认状态为未配货
        if (StringUtil.isEmpty(depotHead.getStatus())) {
            depotHead.setStatus(STATUS_UNPICKED);
        }
        
        // 设置创建时间
        depotHead.setCreateTime(new Date());
        if (depotHead.getOperTime() == null) {
            depotHead.setOperTime(new Date());
        }
        
        // 生成配货单编号
        if (StringUtil.isEmpty(depotHead.getNumber())) {
            depotHead.setNumber(PICKING_PREFIX + sequenceService.buildOnlyNumber());
        }
        
        // 设置创建人
        User userInfo = userService.getCurrentUser();
        depotHead.setCreator(userInfo.getId());
        depotHead.setTenantId(userInfo.getTenantId());
        
        // 插入配货单头
        int result = depotHeadMapper.insertSelective(depotHead);
        
        // 处理配货单明细
        String inserted = obj.getString("inserted");
        if (StringUtil.isNotEmpty(inserted)) {
            depotItemService.saveDetials(inserted, depotHead.getId(), "add", request);
        }
        
        return result;
    }
    
    /**
     * 更新配货单
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int updatePickingListWithDetail(JSONObject obj, HttpServletRequest request) throws Exception {
        
        DepotHead depotHead = JSONObject.parseObject(obj.toJSONString(), DepotHead.class);
        
        // 验证是否为配货单
        DepotHead existingHead = depotHeadMapper.selectByPrimaryKey(depotHead.getId());
        if (existingHead == null || !PICKING_TYPE.equals(existingHead.getType())
            || !PICKING_SUB_TYPE.equals(existingHead.getSubType())) {
            throw new BusinessRunTimeException(ExceptionConstants.DEPOT_HEAD_EDIT_FAILED_CODE,
                "配货单不存在或类型不匹配");
        }
        
        // 保持配货单类型不变
        depotHead.setType(PICKING_TYPE);
        depotHead.setSubType(PICKING_SUB_TYPE);
        
        // 更新配货单头
        int result = depotHeadMapper.updateByPrimaryKeySelective(depotHead);
        
        // 处理配货单明细 - 使用saveDetials方法统一处理
        String rows = obj.getString("rows");
        if (StringUtil.isNotEmpty(rows)) {
            depotItemService.saveDetials(rows, depotHead.getId(), "update", request);
        }
        
        return result;
    }
    
    /**
     * 删除配货单
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int deletePickingList(Long id, HttpServletRequest request) throws Exception {
        
        // 验证是否为配货单
        DepotHead depotHead = depotHeadMapper.selectByPrimaryKey(id);
        if (depotHead == null || !PICKING_TYPE.equals(depotHead.getType())
            || !PICKING_SUB_TYPE.equals(depotHead.getSubType())) {
            throw new BusinessRunTimeException(ExceptionConstants.DEPOT_HEAD_DELETE_FAILED_CODE,
                "配货单不存在或类型不匹配");
        }

        // 检查是否可以删除（已完成且有关联销售单的不能删除）
        if (STATUS_COMPLETED.equals(depotHead.getStatus()) && hasLinkedSaleBill(id)) {
            throw new BusinessRunTimeException(ExceptionConstants.DEPOT_HEAD_DELETE_FAILED_CODE,
                "配货单已完成且有关联销售单，不能删除");
        }
        
        return depotHeadService.batchDeleteBillByIds(id.toString());
    }
    
    /**
     * 批量删除配货单
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int batchDeletePickingList(String ids, HttpServletRequest request) throws Exception {
        
        List<Long> idList = StringUtil.strToLongList(ids);
        int result = 0;
        
        for (Long id : idList) {
            result += deletePickingList(id, request);
        }
        
        return result;
    }
    
    /**
     * 完成配货
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int completePickingOrder(Long id) throws Exception {
        
        DepotHead depotHead = depotHeadMapper.selectByPrimaryKey(id);
        if (depotHead == null || !PICKING_TYPE.equals(depotHead.getType())
            || !PICKING_SUB_TYPE.equals(depotHead.getSubType())) {
            throw new BusinessRunTimeException(ExceptionConstants.DEPOT_HEAD_EDIT_FAILED_CODE,
                "配货单不存在或类型不匹配");
        }

        // 只有配货中状态才能完成
        if (!STATUS_PICKING.equals(depotHead.getStatus())) {
            throw new BusinessRunTimeException(ExceptionConstants.DEPOT_HEAD_EDIT_FAILED_CODE,
                "只有配货中状态的配货单才能完成");
        }
        
        depotHead.setStatus(STATUS_COMPLETED);
        return depotHeadMapper.updateByPrimaryKeySelective(depotHead);
    }
    
    /**
     * 撤销完成
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int revertPickingOrder(Long id) throws Exception {
        
        DepotHead depotHead = depotHeadMapper.selectByPrimaryKey(id);
        if (depotHead == null || !PICKING_TYPE.equals(depotHead.getType())
            || !PICKING_SUB_TYPE.equals(depotHead.getSubType())) {
            throw new BusinessRunTimeException(ExceptionConstants.DEPOT_HEAD_EDIT_FAILED_CODE,
                "配货单不存在或类型不匹配");
        }

        // 只有已完成状态才能撤销
        if (!STATUS_COMPLETED.equals(depotHead.getStatus())) {
            throw new BusinessRunTimeException(ExceptionConstants.DEPOT_HEAD_EDIT_FAILED_CODE,
                "只有已完成状态的配货单才能撤销");
        }

        // 检查是否有关联的销售单
        if (hasLinkedSaleBill(id)) {
            throw new BusinessRunTimeException(ExceptionConstants.DEPOT_HEAD_EDIT_FAILED_CODE,
                "配货单已有关联销售单，不能撤销完成状态");
        }
        
        depotHead.setStatus(STATUS_PICKING);
        return depotHeadMapper.updateByPrimaryKeySelective(depotHead);
    }
    
    /**
     * 批量设置配货状态
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int batchSetPickingStatus(String ids, String status) throws Exception {
        
        List<Long> idList = StringUtil.strToLongList(ids);
        int result = 0;
        
        for (Long id : idList) {
            if (STATUS_COMPLETED.equals(status)) {
                result += completePickingOrder(id);
            } else if (STATUS_PICKING.equals(status)) {
                result += revertPickingOrder(id);
            }
        }
        
        return result;
    }
    
    /**
     * 检查是否有关联的销售单
     */
    private boolean hasLinkedSaleBill(Long pickingListId) {
        // TODO: 实现检查逻辑，查询是否有销售单关联了这个配货单
        // 这里暂时返回false，后续根据业务需求实现
        return false;
    }
}
