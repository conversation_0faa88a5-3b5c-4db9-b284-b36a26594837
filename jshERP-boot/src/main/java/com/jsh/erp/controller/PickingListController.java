package com.jsh.erp.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.constants.ExceptionConstants;
import com.jsh.erp.datasource.vo.DepotHeadVo4List;
import com.jsh.erp.exception.BusinessRunTimeException;
import com.jsh.erp.service.PickingListService;
import com.jsh.erp.utils.BaseResponseInfo;
import com.jsh.erp.utils.ErpInfo;
import com.jsh.erp.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配货单控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-07
 */
@RestController
@RequestMapping(value = "/pickingList")
public class PickingListController {
    
    private Logger logger = LoggerFactory.getLogger(PickingListController.class);
    
    @Resource
    private PickingListService pickingListService;
    
    /**
     * 获取配货单列表
     */
    @GetMapping(value = "/list")
    public BaseResponseInfo getPickingList(@RequestParam("search") String search,
                                          @RequestParam("currentPage") Integer currentPage,
                                          @RequestParam("pageSize") Integer pageSize,
                                          HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        Map<String, Object> map = new HashMap<>();
        
        try {
            List<DepotHeadVo4List> dataList = pickingListService.select(
                null, null, null, null, null, null, null, null,
                (currentPage - 1) * pageSize, pageSize);

            Long total = pickingListService.countPickingList(
                null, null, null, null, null, null, null, null);
            
            map.put("total", total);
            map.put("rows", dataList);
            res.code = 200;
            res.data = map;
        } catch (Exception e) {
            logger.error("获取配货单列表失败", e);
            res.code = 500;
            res.data = "获取配货单列表失败";
        }
        
        return res;
    }
    
    /**
     * 根据条件查询配货单列表
     */
    @PostMapping(value = "/findBySelectWithPage")
    public BaseResponseInfo findBySelectWithPage(@RequestBody JSONObject obj,
                                                HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        Map<String, Object> map = new HashMap<>();
        
        try {
            String number = obj.getString("number");
            String materialParam = obj.getString("materialParam");
            Long organId = obj.getLong("organId");
            String linkNumber = obj.getString("linkNumber");
            String pickingStatus = obj.getString("pickingStatus");
            Long creator = obj.getLong("creator");
            String beginTime = obj.getString("beginTime");
            String endTime = obj.getString("endTime");
            Integer currentPage = obj.getInteger("currentPage");
            Integer pageSize = obj.getInteger("pageSize");
            
            List<DepotHeadVo4List> dataList = pickingListService.select(
                number, materialParam, organId, linkNumber, pickingStatus, creator,
                beginTime, endTime, (currentPage - 1) * pageSize, pageSize);
            
            Long total = pickingListService.countPickingList(
                number, materialParam, organId, linkNumber, pickingStatus, creator,
                beginTime, endTime);
            
            map.put("total", total);
            map.put("rows", dataList);
            res.code = 200;
            res.data = map;
        } catch (Exception e) {
            logger.error("查询配货单列表失败", e);
            res.code = 500;
            res.data = "查询配货单列表失败";
        }
        
        return res;
    }
    
    /**
     * 新增配货单
     */
    @PostMapping(value = "/addPickingListWithDetail")
    public Object addPickingListWithDetail(@RequestBody JSONObject obj,
                                          HttpServletRequest request) throws Exception {
        JSONObject result = ExceptionConstants.standardSuccess();
        
        try {
            pickingListService.insertPickingListWithDetail(obj, request);
        } catch (BusinessRunTimeException e) {
            result = ExceptionConstants.standardError(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("新增配货单失败", e);
            result = ExceptionConstants.standardError(500, "新增配货单失败");
        }
        
        return result;
    }
    
    /**
     * 更新配货单
     */
    @PostMapping(value = "/updatePickingListWithDetail")
    public Object updatePickingListWithDetail(@RequestBody JSONObject obj,
                                             HttpServletRequest request) throws Exception {
        JSONObject result = ExceptionConstants.standardSuccess();
        
        try {
            pickingListService.updatePickingListWithDetail(obj, request);
        } catch (BusinessRunTimeException e) {
            result = ExceptionConstants.standardError(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("更新配货单失败", e);
            result = ExceptionConstants.standardError(500, "更新配货单失败");
        }
        
        return result;
    }
    
    /**
     * 删除配货单
     */
    @PostMapping(value = "/deletePickingList")
    public Object deletePickingList(@RequestBody JSONObject obj,
                                   HttpServletRequest request) throws Exception {
        JSONObject result = ExceptionConstants.standardSuccess();
        
        try {
            Long id = obj.getLong("id");
            if (id == null) {
                return ExceptionConstants.standardError(500, "配货单ID不能为空");
            }
            
            pickingListService.deletePickingList(id, request);
        } catch (BusinessRunTimeException e) {
            result = ExceptionConstants.standardError(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("删除配货单失败", e);
            result = ExceptionConstants.standardError(500, "删除配货单失败");
        }
        
        return result;
    }
    
    /**
     * 批量删除配货单
     */
    @PostMapping(value = "/batchDeletePickingList")
    public Object batchDeletePickingList(@RequestBody JSONObject obj,
                                        HttpServletRequest request) throws Exception {
        JSONObject result = ExceptionConstants.standardSuccess();
        
        try {
            String ids = obj.getString("ids");
            if (StringUtil.isEmpty(ids)) {
                return ExceptionConstants.standardError(500, "配货单ID列表不能为空");
            }
            
            pickingListService.batchDeletePickingList(ids, request);
        } catch (BusinessRunTimeException e) {
            result = ExceptionConstants.standardError(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("批量删除配货单失败", e);
            result = ExceptionConstants.standardError(500, "批量删除配货单失败");
        }
        
        return result;
    }
    
    /**
     * 完成配货
     */
    @PostMapping(value = "/completePickingOrder")
    public Object completePickingOrder(@RequestBody JSONObject obj,
                                      HttpServletRequest request) throws Exception {
        JSONObject result = ExceptionConstants.standardSuccess();
        
        try {
            Long id = obj.getLong("id");
            if (id == null) {
                return ExceptionConstants.standardError(500, "配货单ID不能为空");
            }
            
            pickingListService.completePickingOrder(id);
        } catch (BusinessRunTimeException e) {
            result = ExceptionConstants.standardError(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("完成配货失败", e);
            result = ExceptionConstants.standardError(500, "完成配货失败");
        }
        
        return result;
    }
    
    /**
     * 撤销完成
     */
    @PostMapping(value = "/revertPickingOrder")
    public Object revertPickingOrder(@RequestBody JSONObject obj,
                                    HttpServletRequest request) throws Exception {
        JSONObject result = ExceptionConstants.standardSuccess();
        
        try {
            Long id = obj.getLong("id");
            if (id == null) {
                return ExceptionConstants.standardError(500, "配货单ID不能为空");
            }
            
            pickingListService.revertPickingOrder(id);
        } catch (BusinessRunTimeException e) {
            result = ExceptionConstants.standardError(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("撤销完成失败", e);
            result = ExceptionConstants.standardError(500, "撤销完成失败");
        }
        
        return result;
    }
    
    /**
     * 批量设置配货状态
     */
    @PostMapping(value = "/batchSetPickingStatus")
    public Object batchSetPickingStatus(@RequestBody JSONObject obj,
                                       HttpServletRequest request) throws Exception {
        JSONObject result = ExceptionConstants.standardSuccess();
        
        try {
            String ids = obj.getString("ids");
            String status = obj.getString("status");
            
            if (StringUtil.isEmpty(ids)) {
                return ExceptionConstants.standardError(500, "配货单ID列表不能为空");
            }
            
            if (StringUtil.isEmpty(status)) {
                return ExceptionConstants.standardError(500, "配货状态不能为空");
            }
            
            pickingListService.batchSetPickingStatus(ids, status);
        } catch (BusinessRunTimeException e) {
            result = ExceptionConstants.standardError(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("批量设置配货状态失败", e);
            result = ExceptionConstants.standardError(500, "批量设置配货状态失败");
        }
        
        return result;
    }
}
