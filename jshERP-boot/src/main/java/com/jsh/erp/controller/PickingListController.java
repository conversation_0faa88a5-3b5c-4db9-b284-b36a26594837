package com.jsh.erp.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.base.BaseController;
import com.jsh.erp.base.TableDataInfo;
import com.jsh.erp.constants.ExceptionConstants;
import com.jsh.erp.datasource.vo.DepotHeadVo4List;
import com.jsh.erp.service.PickingListService;
import com.jsh.erp.utils.Constants;
import com.jsh.erp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jsh.erp.utils.ResponseJsonUtil.returnJson;
import static com.jsh.erp.utils.ResponseJsonUtil.returnStr;

/**
 * 配货单控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-07-07
 */
@RestController
@RequestMapping(value = "/pickingList")
@Api(tags = "配货单管理")
public class PickingListController extends BaseController {

    @Resource
    private PickingListService pickingListService;
    
    /**
     * 查询配货单列表
     */
    @GetMapping(value = "/list")
    @ApiOperation(value = "获取配货单列表")
    public TableDataInfo getList(@RequestParam(value = Constants.SEARCH, required = false) String search,
                                 HttpServletRequest request) throws Exception {
        String number = StringUtil.getInfo(search, "number");
        String status = StringUtil.getInfo(search, "status");
        String beginTime = StringUtil.getInfo(search, "beginTime");
        String endTime = StringUtil.getInfo(search, "endTime");
        String materialParam = StringUtil.getInfo(search, "materialParam");
        Long organId = StringUtil.parseStrLong(StringUtil.getInfo(search, "organId"));
        String linkNumber = StringUtil.getInfo(search, "linkNumber");
        Long creator = StringUtil.parseStrLong(StringUtil.getInfo(search, "creator"));
        String remark = StringUtil.getInfo(search, "remark");

        List<DepotHeadVo4List> list = pickingListService.select(number, materialParam, organId,
                linkNumber, status, creator, beginTime, endTime, remark);
        return getDataTable(list);
    }

    /**
     * 分页查询配货单列表
     */
    @PostMapping(value = "/findBySelectWithPage")
    @ApiOperation(value = "分页查询配货单列表")
    public String findBySelectWithPage(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        try {
            String number = obj.getString("number");
            String materialParam = obj.getString("materialParam");
            Long organId = obj.getLong("organId");
            String linkNumber = obj.getString("linkNumber");
            String status = obj.getString("status");
            Long creator = obj.getLong("creator");
            String beginTime = obj.getString("beginTime");
            String endTime = obj.getString("endTime");
            String remark = obj.getString("remark");
            Integer currentPage = obj.getInteger("currentPage");
            Integer pageSize = obj.getInteger("pageSize");

            // 设置分页
            if (currentPage != null && pageSize != null) {
                startPage();
            }

            List<DepotHeadVo4List> list = pickingListService.select(number, materialParam, organId,
                    linkNumber, status, creator, beginTime, endTime, remark);

            objectMap.put("rows", list);
            objectMap.put("total", list.size());
            return returnJson(objectMap, "查询成功", 200);
        } catch (Exception e) {
            logger.error("查询配货单列表失败", e);
            objectMap.put("rows", new ArrayList<>());
            objectMap.put("total", 0);
            return returnJson(objectMap, "查询失败", 500);
        }
    }
    
    /**
     * 根据ID获取配货单详情
     */
    @GetMapping(value = "/info")
    @ApiOperation(value = "获取配货单详情")
    public Object getInfo(@RequestParam("id") Long id, HttpServletRequest request) throws Exception {
        List<DepotHeadVo4List> list = pickingListService.getDetailById(id, request);
        JSONObject result = new JSONObject();
        result.put("data", list);
        return result;
    }
    
    /**
     * 新增配货单
     */
    @PostMapping(value = "/addPickingListWithDetail")
    @ApiOperation(value = "新增配货单")
    public String addPickingListWithDetail(@RequestBody JSONObject obj,
                                          HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        String beanJson = obj.getString("info");
        String rows = obj.getString("rows");
        int insert = pickingListService.insertPickingListWithDetail(beanJson, rows, request);
        return returnStr(objectMap, insert);
    }
    
    /**
     * 更新配货单
     */
    @PostMapping(value = "/updatePickingListWithDetail")
    @ApiOperation(value = "更新配货单")
    public String updatePickingListWithDetail(@RequestBody JSONObject obj,
                                            HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        int update = pickingListService.updatePickingListWithDetail(obj, request);
        return returnStr(objectMap, update);
    }

    /**
     * 删除配货单
     */
    @PostMapping(value = "/deletePickingList")
    @ApiOperation(value = "删除配货单")
    public String deletePickingList(@RequestBody JSONObject obj,
                                   HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        Long id = obj.getLong("id");
        int delete = pickingListService.deletePickingList(id, request);
        return returnStr(objectMap, delete);
    }

    /**
     * 批量删除配货单
     */
    @PostMapping(value = "/batchDeletePickingList")
    @ApiOperation(value = "批量删除配货单")
    public String batchDeletePickingList(@RequestBody JSONObject obj,
                                        HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        String ids = obj.getString("ids");
        int delete = pickingListService.batchDeletePickingList(ids, request);
        return returnStr(objectMap, delete);
    }
    
    /**
     * 完成配货
     */
    @PostMapping(value = "/completePickingOrder")
    @ApiOperation(value = "完成配货")
    public String completePickingOrder(@RequestBody JSONObject obj) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        Long id = obj.getLong("id");
        int result = pickingListService.completePickingOrder(id);
        return returnStr(objectMap, result);
    }

    /**
     * 撤销完成
     */
    @PostMapping(value = "/revertPickingOrder")
    @ApiOperation(value = "撤销完成")
    public String revertPickingOrder(@RequestBody JSONObject obj) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        Long id = obj.getLong("id");
        int result = pickingListService.revertPickingOrder(id);
        return returnStr(objectMap, result);
    }

    /**
     * 批量设置配货状态
     */
    @PostMapping(value = "/batchSetPickingStatus")
    @ApiOperation(value = "批量设置配货状态")
    public String batchSetPickingStatus(@RequestBody JSONObject obj) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        String ids = obj.getString("ids");
        String status = obj.getString("status");
        int result = pickingListService.batchSetPickingStatus(ids, status);
        return returnStr(objectMap, result);
    }
}
