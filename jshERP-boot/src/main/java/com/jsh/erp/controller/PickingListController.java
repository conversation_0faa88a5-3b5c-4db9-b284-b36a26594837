package com.jsh.erp.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.base.BaseController;
import com.jsh.erp.base.TableDataInfo;
import com.jsh.erp.constants.Constants;
import com.jsh.erp.constants.ExceptionConstants;
import com.jsh.erp.datasource.vo.DepotHeadVo4List;
import com.jsh.erp.service.PickingListService;
import com.jsh.erp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 配货单控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-07-07
 */
@RestController
@RequestMapping(value = "/pickingList")
@Api(tags = "配货单管理")
public class PickingListController extends BaseController {

    @Resource
    private PickingListService pickingListService;
    
    /**
     * 查询配货单列表
     */
    @GetMapping(value = "/list")
    @ApiOperation(value = "获取配货单列表")
    public TableDataInfo getList(@RequestParam(value = Constants.SEARCH, required = false) String search,
                                 HttpServletRequest request) throws Exception {
        String number = StringUtil.getInfo(search, "number");
        String status = StringUtil.getInfo(search, "status");
        String beginTime = StringUtil.getInfo(search, "beginTime");
        String endTime = StringUtil.getInfo(search, "endTime");
        String materialParam = StringUtil.getInfo(search, "materialParam");
        Long organId = StringUtil.parseStrLong(StringUtil.getInfo(search, "organId"));
        String linkNumber = StringUtil.getInfo(search, "linkNumber");
        Long creator = StringUtil.parseStrLong(StringUtil.getInfo(search, "creator"));
        String remark = StringUtil.getInfo(search, "remark");

        List<DepotHeadVo4List> list = pickingListService.select(number, materialParam, organId,
                linkNumber, status, creator, beginTime, endTime, remark);
        return getDataTable(list);
    }
    
    /**
     * 根据ID获取配货单详情
     */
    @GetMapping(value = "/info")
    @ApiOperation(value = "获取配货单详情")
    public Object getInfo(@RequestParam("id") Long id, HttpServletRequest request) throws Exception {
        List<DepotHeadVo4List> list = pickingListService.getDetailById(id, request);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 新增配货单
     */
    @PostMapping(value = "/addPickingListWithDetail")
    @ApiOperation(value = "新增配货单")
    public Object addPickingListWithDetail(@RequestBody JSONObject obj,
                                          HttpServletRequest request) throws Exception {
        pickingListService.insertPickingListWithDetail(obj, request);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 更新配货单
     */
    @PostMapping(value = "/updatePickingListWithDetail")
    @ApiOperation(value = "更新配货单")
    public Object updatePickingListWithDetail(@RequestBody JSONObject obj,
                                            HttpServletRequest request) throws Exception {
        pickingListService.updatePickingListWithDetail(obj, request);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 删除配货单
     */
    @PostMapping(value = "/deletePickingList")
    public Object deletePickingList(@RequestBody JSONObject obj,
                                   HttpServletRequest request) throws Exception {
        Long id = obj.getLong("id");
        pickingListService.deletePickingList(id, request);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 批量删除配货单
     */
    @PostMapping(value = "/batchDeletePickingList")
    public Object batchDeletePickingList(@RequestBody JSONObject obj,
                                        HttpServletRequest request) throws Exception {
        String ids = obj.getString("ids");
        pickingListService.batchDeletePickingList(ids, request);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 完成配货
     */
    @PostMapping(value = "/completePickingOrder")
    public Object completePickingOrder(@RequestBody JSONObject obj) throws Exception {
        Long id = obj.getLong("id");
        pickingListService.completePickingOrder(id);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 撤销完成
     */
    @PostMapping(value = "/revertPickingOrder")
    public Object revertPickingOrder(@RequestBody JSONObject obj) throws Exception {
        Long id = obj.getLong("id");
        pickingListService.revertPickingOrder(id);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 批量设置配货状态
     */
    @PostMapping(value = "/batchSetPickingStatus")
    public Object batchSetPickingStatus(@RequestBody JSONObject obj) throws Exception {
        String ids = obj.getString("ids");
        String status = obj.getString("status");
        pickingListService.batchSetPickingStatus(ids, status);
        return ExceptionConstants.standardSuccess();
    }
}
