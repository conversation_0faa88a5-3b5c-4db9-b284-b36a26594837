package com.jsh.erp.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.base.BaseController;
import com.jsh.erp.base.TableDataInfo;
import com.jsh.erp.constants.Constants;
import com.jsh.erp.constants.ExceptionConstants;
import com.jsh.erp.datasource.vo.DepotHeadVo4List;
import com.jsh.erp.service.PickingListService;
import com.jsh.erp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 配货单控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-07
 */
@RestController
@RequestMapping(value = "/pickingList")
public class PickingListController {
    
    private Logger logger = LoggerFactory.getLogger(PickingListController.class);
    
    @Resource
    private PickingListService pickingListService;
    
    /**
     * 查询配货单列表
     */
    @GetMapping(value = "/list")
    public BaseResponseInfo getPickingListList(@RequestParam("currentPage") Integer currentPage,
                                              @RequestParam("pageSize") Integer pageSize,
                                              @RequestParam(value = "number", required = false) String number,
                                              @RequestParam(value = "status", required = false) String status,
                                              @RequestParam(value = "beginTime", required = false) String beginTime,
                                              @RequestParam(value = "endTime", required = false) String endTime,
                                              HttpServletRequest request) throws Exception {
        
        BaseResponseInfo res = new BaseResponseInfo();
        Map<String, Object> map = new HashMap<>();
        
        List<DepotHeadVo4List> dataList = pickingListService.select(number, null, null,
            null, status, null, beginTime, endTime, (currentPage-1)*pageSize, pageSize);
        Long total = pickingListService.countPickingList(number, null, null,
            null, status, null, beginTime, endTime);
        
        map.put("rows", dataList);
        map.put("total", total);
        res.code = 200;
        res.data = map;
        
        return res;
    }
    
    /**
     * 根据ID获取配货单详情
     */
    @GetMapping(value = "/info")
    public String getPickingListInfo(@RequestParam("id") Long id, HttpServletRequest request) throws Exception {
        
        Map<String, Object> objectMap = new HashMap<>();
        List<DepotHeadVo4List> list = pickingListService.getDetailById(id, request);
        
        if (list != null && !list.isEmpty()) {
            objectMap.put("info", list.get(0));
            return returnJson(objectMap, ErpInfo.OK.name, ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, ErpInfo.ERROR.name, ErpInfo.ERROR.code);
        }
    }
    
    /**
     * 通用返回JSON方法
     */
    public String returnJson(Object object, String message, Integer code) {
        JSONObject result = new JSONObject();
        result.put("code", code);
        result.put("msg", message);
        result.put("data", object);
        return result.toJSONString();
    }
    
    /**
     * 新增配货单
     */
    @PostMapping(value = "/addPickingListWithDetail")
    public Object addPickingListWithDetail(@RequestBody JSONObject obj,
                                          HttpServletRequest request) throws Exception {
        pickingListService.insertPickingListWithDetail(obj, request);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 更新配货单
     */
    @PostMapping(value = "/updatePickingListWithDetail")
    public Object updatePickingListWithDetail(@RequestBody JSONObject obj,
                                            HttpServletRequest request) throws Exception {
        pickingListService.updatePickingListWithDetail(obj, request);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 删除配货单
     */
    @PostMapping(value = "/deletePickingList")
    public Object deletePickingList(@RequestBody JSONObject obj,
                                   HttpServletRequest request) throws Exception {
        Long id = obj.getLong("id");
        pickingListService.deletePickingList(id, request);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 批量删除配货单
     */
    @PostMapping(value = "/batchDeletePickingList")
    public Object batchDeletePickingList(@RequestBody JSONObject obj,
                                        HttpServletRequest request) throws Exception {
        String ids = obj.getString("ids");
        pickingListService.batchDeletePickingList(ids, request);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 完成配货
     */
    @PostMapping(value = "/completePickingOrder")
    public Object completePickingOrder(@RequestBody JSONObject obj) throws Exception {
        Long id = obj.getLong("id");
        pickingListService.completePickingOrder(id);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 撤销完成
     */
    @PostMapping(value = "/revertPickingOrder")
    public Object revertPickingOrder(@RequestBody JSONObject obj) throws Exception {
        Long id = obj.getLong("id");
        pickingListService.revertPickingOrder(id);
        return ExceptionConstants.standardSuccess();
    }
    
    /**
     * 批量设置配货状态
     */
    @PostMapping(value = "/batchSetPickingStatus")
    public Object batchSetPickingStatus(@RequestBody JSONObject obj) throws Exception {
        String ids = obj.getString("ids");
        String status = obj.getString("status");
        pickingListService.batchSetPickingStatus(ids, status);
        return ExceptionConstants.standardSuccess();
    }
}
