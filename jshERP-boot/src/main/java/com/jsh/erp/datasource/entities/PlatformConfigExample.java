package com.jsh.erp.datasource.entities;

import java.util.ArrayList;
import java.util.List;

public class PlatformConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PlatformConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyIsNull() {
            addCriterion("platform_key is null");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyIsNotNull() {
            addCriterion("platform_key is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyEqualTo(String value) {
            addCriterion("platform_key =", value, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyNotEqualTo(String value) {
            addCriterion("platform_key <>", value, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyGreaterThan(String value) {
            addCriterion("platform_key >", value, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyGreaterThanOrEqualTo(String value) {
            addCriterion("platform_key >=", value, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyLessThan(String value) {
            addCriterion("platform_key <", value, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyLessThanOrEqualTo(String value) {
            addCriterion("platform_key <=", value, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyLike(String value) {
            addCriterion("platform_key like", value, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyNotLike(String value) {
            addCriterion("platform_key not like", value, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyIn(List<String> values) {
            addCriterion("platform_key in", values, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyNotIn(List<String> values) {
            addCriterion("platform_key not in", values, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyBetween(String value1, String value2) {
            addCriterion("platform_key between", value1, value2, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyNotBetween(String value1, String value2) {
            addCriterion("platform_key not between", value1, value2, "platformKey");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoIsNull() {
            addCriterion("platform_key_info is null");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoIsNotNull() {
            addCriterion("platform_key_info is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoEqualTo(String value) {
            addCriterion("platform_key_info =", value, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoNotEqualTo(String value) {
            addCriterion("platform_key_info <>", value, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoGreaterThan(String value) {
            addCriterion("platform_key_info >", value, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoGreaterThanOrEqualTo(String value) {
            addCriterion("platform_key_info >=", value, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoLessThan(String value) {
            addCriterion("platform_key_info <", value, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoLessThanOrEqualTo(String value) {
            addCriterion("platform_key_info <=", value, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoLike(String value) {
            addCriterion("platform_key_info like", value, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoNotLike(String value) {
            addCriterion("platform_key_info not like", value, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoIn(List<String> values) {
            addCriterion("platform_key_info in", values, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoNotIn(List<String> values) {
            addCriterion("platform_key_info not in", values, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoBetween(String value1, String value2) {
            addCriterion("platform_key_info between", value1, value2, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformKeyInfoNotBetween(String value1, String value2) {
            addCriterion("platform_key_info not between", value1, value2, "platformKeyInfo");
            return (Criteria) this;
        }

        public Criteria andPlatformValueIsNull() {
            addCriterion("platform_value is null");
            return (Criteria) this;
        }

        public Criteria andPlatformValueIsNotNull() {
            addCriterion("platform_value is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformValueEqualTo(String value) {
            addCriterion("platform_value =", value, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueNotEqualTo(String value) {
            addCriterion("platform_value <>", value, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueGreaterThan(String value) {
            addCriterion("platform_value >", value, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueGreaterThanOrEqualTo(String value) {
            addCriterion("platform_value >=", value, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueLessThan(String value) {
            addCriterion("platform_value <", value, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueLessThanOrEqualTo(String value) {
            addCriterion("platform_value <=", value, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueLike(String value) {
            addCriterion("platform_value like", value, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueNotLike(String value) {
            addCriterion("platform_value not like", value, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueIn(List<String> values) {
            addCriterion("platform_value in", values, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueNotIn(List<String> values) {
            addCriterion("platform_value not in", values, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueBetween(String value1, String value2) {
            addCriterion("platform_value between", value1, value2, "platformValue");
            return (Criteria) this;
        }

        public Criteria andPlatformValueNotBetween(String value1, String value2) {
            addCriterion("platform_value not between", value1, value2, "platformValue");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}