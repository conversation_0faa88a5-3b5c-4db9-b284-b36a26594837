<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <classPathEntry
            location="E:\maven\maven-repository\mysql\mysql-connector-java\5.1.30\mysql-connector-java-5.1.30.jar"/>

    <context id="DB2Tables" targetRuntime="MyBatis3" defaultModelType="flat">
        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="************************************************************************"
                        userId="root" password="123456">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- generate Model -->
        <javaModelGenerator targetPackage="com.jsh.erp.datasource.entities"
                            targetProject="src\main\java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- generate xml -->
        <sqlMapGenerator targetPackage="mapper_xml" targetProject="src\main\resources">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        <!-- generate Mapper -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.jsh.erp.datasource.mappers"
                             targetProject="src\main\java">
            <property name="enableSubPackages" value="false"/>
            <property name="exampleMethodVisibility" value="public"/>
        </javaClientGenerator>
        <!-- <table tableName="jsh_account" domainObjectName="Account"></table>
        <table tableName="jsh_account_head" domainObjectName="AccountHead"></table>
        <table tableName="jsh_account_item" domainObjectName="AccountItem"></table>
        <table tableName="jsh_depot" domainObjectName="Depot"></table>
        <table tableName="jsh_depot_head" domainObjectName="DepotHead"></table>
        <table tableName="jsh_depot_item" domainObjectName="DepotItem"></table>
        <table tableName="jsh_function" domainObjectName="Function"></table>
        <table tableName="jsh_in_out_item" domainObjectName="InOutItem"></table>
        <table tableName="jsh_log" domainObjectName="Log"></table>
        <table tableName="jsh_material" domainObjectName="Material"></table>
        <table tableName="jsh_material_attribute" domainObjectName="MaterialAttribute"></table>
        <table tableName="jsh_material_extend" domainObjectName="MaterialExtend"></table>
        <table tableName="jsh_material_current_stock" domainObjectName="MaterialCurrentStock"></table>
        <table tableName="jsh_material_initial_stock" domainObjectName="MaterialInitialStock"></table>
        <table tableName="jsh_material_category" domainObjectName="MaterialCategory"></table>
        <table tableName="jsh_material_property" domainObjectName="MaterialProperty"></table>
        <table tableName="jsh_person" domainObjectName="Person"></table>
        <table tableName="jsh_role" domainObjectName="Role"></table>
        <table tableName="jsh_supplier" domainObjectName="Supplier"></table>
        <table tableName="jsh_system_config" domainObjectName="SystemConfig"></table>
        <table tableName="jsh_unit" domainObjectName="Unit"></table>
        <table tableName="jsh_user" domainObjectName="User"></table>
        <table tableName="jsh_user_business" domainObjectName="UserBusiness"></table>
        <table tableName="jsh_serial_number" domainObjectName="SerialNumber"></table>
        <table tableName="jsh_organization" domainObjectName="Organization"></table>
        <table tableName="jsh_orga_user_rel" domainObjectName="OrgaUserRel"></table>
        <table tableName="jsh_tenant" domainObjectName="Tenant"></table>
        <table tableName="jsh_platform_config" domainObjectName="PlatformConfig"></table>
        <table tableName="jsh_msg" domainObjectName="Msg"></table> -->
    </context>
</generatorConfiguration>
