-- 添加配货单功能菜单配置
-- 在销售管理(0603)下添加配货单功能

-- 1. 插入或更新配货单功能菜单
-- 如果之前已经插入过，请使用UPDATE语句：
-- UPDATE `jsh_function` SET `component` = '/bill/PickingListList' WHERE `id` = 262;
-- 如果没有插入过，请使用INSERT语句：
INSERT INTO `jsh_function` VALUES ('262', '060302', '配货单', '0603', '/bill/picking_list', '/bill/PickingListList', '\0', '0393', '', '电脑版', '1,2,3,7', 'profile', '0')
ON DUPLICATE KEY UPDATE `component` = '/bill/PickingListList';

-- 2. 更新管理员角色(ID=4)的功能权限，在现有权限中添加配货单功能ID[262]
-- 在销售订单[242]后面插入配货单[262]
UPDATE `jsh_user_business`
SET `value` = REPLACE(`value`, '[242][38]', '[242][262][38]'),
    `btn_str` = CONCAT(SUBSTRING(`btn_str`, 1, LENGTH(`btn_str`)-1), ',{"funId":262,"btnStr":"1,2,7,3"}]')
WHERE `type` = 'RoleFunctions' AND `key_id` = '4';

-- 3. 更新租户角色(ID=10)的功能权限，在现有权限中添加配货单功能ID[262]
-- 在销售订单[242]后面插入配货单[262]
UPDATE `jsh_user_business`
SET `value` = REPLACE(`value`, '[242][38]', '[242][262][38]'),
    `btn_str` = CONCAT(SUBSTRING(`btn_str`, 1, LENGTH(`btn_str`)-1), ',{"funId":262,"btnStr":"1,2,7,3"}]')
WHERE `type` = 'RoleFunctions' AND `key_id` = '10';
