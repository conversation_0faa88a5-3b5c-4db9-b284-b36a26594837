<!DOCTYPE html>
<html>
<head>
    <title>配货单功能测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>配货单功能测试页面</h1>
        
        <div class="section info">
            <h3>问题诊断</h3>
            <p>配货单页面打不开的可能原因：</p>
            <ol>
                <li><strong>菜单权限配置缺失</strong> - 数据库中没有配货单的菜单配置</li>
                <li><strong>用户权限不足</strong> - 当前用户没有访问配货单的权限</li>
                <li><strong>前端路由问题</strong> - 组件路径配置错误</li>
                <li><strong>后端API问题</strong> - 配货单相关的API接口有问题</li>
            </ol>
        </div>

        <div class="section">
            <h3>解决步骤</h3>
            
            <h4>1. 执行数据库配置脚本</h4>
            <p>需要在数据库中执行以下SQL脚本来添加菜单权限：</p>
            <pre>-- 1. 插入配货单功能菜单
INSERT INTO `jsh_function` VALUES ('262', '060302', '配货单', '0603', '/bill/picking_list', '/bill/PickingList', '\0', '0393', '', '电脑版', '1,2,3,7', 'profile', '0');

-- 2. 更新管理员角色权限
UPDATE `jsh_user_business`
SET `value` = REPLACE(`value`, '[242][38]', '[242][262][38]'),
    `btn_str` = CONCAT(SUBSTRING(`btn_str`, 1, LENGTH(`btn_str`)-1), ',{"funId":262,"btnStr":"1,2,7,3"}]')
WHERE `type` = 'RoleFunctions' AND `key_id` = '4';

-- 3. 更新租户角色权限
UPDATE `jsh_user_business`
SET `value` = REPLACE(`value`, '[242][38]', '[242][262][38]'),
    `btn_str` = CONCAT(SUBSTRING(`btn_str`, 1, LENGTH(`btn_str`)-1), ',{"funId":262,"btnStr":"1,2,7,3"}]')
WHERE `type` = 'RoleFunctions' AND `key_id` = '10';</pre>

            <h4>2. 重启后端服务</h4>
            <p>执行SQL脚本后，需要重启后端服务以刷新权限缓存。</p>

            <h4>3. 清除浏览器缓存并重新登录</h4>
            <p>清除浏览器缓存，重新登录系统以获取最新的菜单权限。</p>
        </div>

        <div class="section">
            <h3>快速测试方法</h3>
            <p>如果您有数据库访问权限，可以按以下步骤操作：</p>
            <ol>
                <li>连接到jshERP数据库</li>
                <li>执行上述SQL脚本</li>
                <li>重启后端服务</li>
                <li>刷新浏览器页面</li>
                <li>重新登录系统</li>
                <li>在销售管理菜单下应该能看到配货单选项</li>
            </ol>
        </div>

        <div class="section">
            <h3>验证配置是否成功</h3>
            <p>执行以下SQL查询来验证配置是否正确：</p>
            <pre>-- 检查配货单菜单是否已添加
SELECT * FROM jsh_function WHERE name = '配货单';

-- 检查管理员角色权限是否包含配货单
SELECT * FROM jsh_user_business WHERE type = 'RoleFunctions' AND key_id = '4';</pre>
        </div>

        <div class="section success">
            <h3>预期结果</h3>
            <p>配置成功后，您应该能够：</p>
            <ul>
                <li>在销售管理菜单下看到"配货单"选项</li>
                <li>点击配货单能正常打开列表页面</li>
                <li>能够新增、编辑、删除配货单</li>
                <li>配货单的所有功能都能正常使用</li>
            </ul>
        </div>

        <div class="section">
            <h3>如果问题仍然存在</h3>
            <p>如果执行上述步骤后问题仍然存在，请检查：</p>
            <ul>
                <li>浏览器控制台是否有JavaScript错误</li>
                <li>网络请求是否正常（F12开发者工具 -> Network）</li>
                <li>后端日志是否有错误信息</li>
                <li>数据库连接是否正常</li>
            </ul>
        </div>
    </div>
</body>
</html>
