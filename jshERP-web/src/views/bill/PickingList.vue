<!-- 配货单列表页面 -->
<template>
  <a-row :gutter="24">
    <a-col :md="24">
      <a-card :style="cardStyle" :bordered="false">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <!-- 搜索区域 -->
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :md="6" :sm="24">
                <a-form-item label="单据编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入配货单编号" v-model="queryParam.number"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="商品信息" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入条码、名称、助记码、规格、型号等信息" v-model="queryParam.materialParam"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="单据日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-range-picker
                    style="width:100%"
                    v-model="queryParam.createTimeRange"
                    format="YYYY-MM-DD"
                    :placeholder="['开始时间', '结束时间']"
                    @change="onDateChange"
                    @ok="onDateOk"
                  />
                </a-form-item>
              </a-col>
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-col :md="6" :sm="24">
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a @click="handleToggleSearch" style="margin-left: 8px">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
                  </a>
                </a-col>
              </span>
            </a-row>
            <template v-if="toggleSearchStatus">
              <a-row :gutter="24">
                <a-col :md="6" :sm="24">
                  <a-form-item label="客户" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select placeholder="请选择客户" showSearch allow-clear optionFilterProp="children" v-model="queryParam.organId">
                      <a-select-option v-for="(item,index) in cusList" :key="index" :value="item.id">
                        {{ item.supplier }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="关联销售订单" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="请输入关联销售订单号" v-model="queryParam.linkNumber"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="配货状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select placeholder="请选择配货状态" allow-clear v-model="queryParam.pickingStatus">
                      <a-select-option value="0">未配货</a-select-option>
                      <a-select-option value="1">配货中</a-select-option>
                      <a-select-option value="2">已完成</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="操作员" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select placeholder="请选择操作员" showSearch allow-clear optionFilterProp="children" v-model="queryParam.creator">
                      <a-select-option v-for="(item,index) in userList" :key="index" :value="item.id">
                        {{ item.userName }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </template>
          </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator" style="margin-top: 5px">
          <a-button v-if="btnEnableList.indexOf(1)>-1" @click="myHandleAdd" type="primary" icon="plus">新增</a-button>
          <a-button v-if="btnEnableList.indexOf(1)>-1" icon="delete" @click="batchDel">删除</a-button>
          <a-button v-if="btnEnableList.indexOf(1)>-1" icon="check" @click="batchSetPickingStatus(2)">批量完成</a-button>
          <a-button v-if="btnEnableList.indexOf(1)>-1" icon="rollback" @click="batchSetPickingStatus(1)">批量撤销完成</a-button>
          <a-button v-if="isShowExcel && btnEnableList.indexOf(3)>-1" icon="download" @click="handleExport">导出</a-button>
        </div>
        <!-- table区域-begin -->
        <div>
          <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
          </div>
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
            @change="handleTableChange">
            <span slot="action" slot-scope="text, record">
              <a @click="myHandleDetail(record, '配货单', prefixNo)">查看</a>
              <a-divider v-if="btnEnableList.indexOf(1)>-1" type="vertical" />
              <a v-if="btnEnableList.indexOf(1)>-1" @click="myHandleEdit(record)">编辑</a>
              <a-divider v-if="btnEnableList.indexOf(1)>-1" type="vertical" />
              <a v-if="btnEnableList.indexOf(1)>-1" @click="myHandleCopyAdd(record)">复制</a>
              <a-divider v-if="btnEnableList.indexOf(1)>-1" type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => myHandleDelete(record.id)">
                <a v-if="btnEnableList.indexOf(1)>-1">删除</a>
              </a-popconfirm>
            </span>
            <span slot="customRenderPickingStatus" slot-scope="text">
              <a-tag v-if="text === '0'" color="red">未配货</a-tag>
              <a-tag v-else-if="text === '1'" color="orange">配货中</a-tag>
              <a-tag v-else-if="text === '2'" color="green">已完成</a-tag>
              <a-tag v-else color="gray">未知</a-tag>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
        <!-- 表单区域 -->
        <picking-modal ref="modalForm" @ok="modalFormOk" @close="modalFormClose"></picking-modal>
        <bill-detail ref="modalDetail" @ok="modalFormOk" @close="modalFormClose"></bill-detail>
        <bill-excel-iframe ref="billExcelIframe" @ok="modalFormOk" @close="modalFormClose"></bill-excel-iframe>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import PickingModal from './modules/PickingModal'
  import BillDetail from './dialog/BillDetail'
  import BillExcelIframe from '@/components/tools/BillExcelIframe'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { BillListMixin } from './mixins/BillListMixin'
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import JDate from '@/components/jeecg/JDate'

  export default {
    name: "PickingList",
    mixins:[JeecgListMixin,BillListMixin],
    components: {
      PickingModal,
      BillDetail,
      BillExcelIframe,
      JEllipsis,
      JDate
    },
    data () {
      return {
        // 查询条件
        queryParam: {
          number: "",
          materialParam: "",
          type: "其它",
          subType: "配货单",
          organId: undefined,
          creator: undefined,
          pickingStatus: undefined,
          linkNumber: "",
          remark: ""
        },
        prefixNo: 'PHD',
        labelCol: {
          span: 5
        },
        wrapperCol: {
          span: 18,
          offset: 1
        },
        // 默认索引
        defDataIndex:['action','organName','number','linkNumber','materialsList','operTimeStr','userName','materialCount','pickingStatus'],
        // 默认列
        defColumns: [
          {
            title: '操作',
            dataIndex: 'action',
            align:"center", width: 180,
            scopedSlots: { customRender: 'action' },
          },
          { title: '客户', dataIndex: 'organName',width:120},
          { title: '单据编号', dataIndex: 'number',width:130},
          { title: '关联销售订单', dataIndex: 'linkNumber',width:130},
          { title: '商品信息', dataIndex: 'materialsList',width:300},
          { title: '单据日期', dataIndex: 'operTimeStr',width:100},
          { title: '操作员', dataIndex: 'userName',width:80},
          { title: '商品数量', dataIndex: 'materialCount',width:80},
          { title: '配货状态', dataIndex: 'pickingStatus', width: 80, align: "center",
            scopedSlots: { customRender: 'customRenderPickingStatus' }
          }
        ],
        url: {
          list: "/pickingList/findBySelectWithPage",
          delete: "/pickingList/deletePickingList",
          deleteBatch: "/pickingList/batchDeletePickingList",
          batchSetPickingStatus: "/pickingList/batchSetPickingStatus"
        }
      }
    },
    created() {
      this.initCustomer()
      this.initUser()
      this.initQuickBtn()
    },
    methods: {
      // 批量设置配货状态
      batchSetPickingStatus(status) {
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！')
          return
        }
        let statusText = status === 2 ? '完成' : '撤销完成'
        let that = this
        this.$confirm({
          title: "确认操作",
          content: `确定要${statusText}选中的配货单吗?`,
          onOk: function () {
            that.loading = true
            let params = {
              ids: that.selectedRowKeys.join(','),
              status: status
            }
            that.$http.post(that.url.batchSetPickingStatus, params).then((res) => {
              if (res.success) {
                that.$message.success(`${statusText}成功`)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.message)
              }
              that.loading = false
            })
          }
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
