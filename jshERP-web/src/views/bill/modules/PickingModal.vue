<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :keyboard="false"
    :forceRender="true"
    v-bind:prefixNo="prefixNo"
    fullscreen
    switchHelp
    switchFullscreen
    @cancel="handleCancel"
    :id="prefixNo"
    style="top:20px;height: 95%;">
    <template slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button v-if="billPrintFlag && isShowPrintBtn" @click="handlePrint('配货单')">打印配货单</a-button>
      <a-button v-if="model.pickingStatus === '1'" :loading="confirmLoading" @click="handleComplete">完成配货</a-button>
      <a-button v-if="model.pickingStatus === '2' && !hasLinkedSaleBill" :loading="confirmLoading" @click="handleRevert">撤销完成</a-button>
      <a-button type="primary" :loading="confirmLoading" @click="handleOk">保存（Ctrl+S）</a-button>
    </template>
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row class="form-row" :gutter="24">
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="客户">
              <a-select placeholder="请选择客户" v-decorator="[ 'organId', validatorRules.organId ]"
                :dropdownMatchSelectWidth="false" showSearch optionFilterProp="children" @change="handleOrganChange">
                <div slot="dropdownRender" slot-scope="menu">
                  <v-nodes :vnodes="menu" />
                  <a-divider style="margin: 4px 0;" />
                  <div v-if="quickBtn.customer" class="dropdown-btn" @mousedown="e => e.preventDefault()" @click="addCustomer"><a-icon type="plus" /> 新增客户</div>
                  <div class="dropdown-btn" @mousedown="e => e.preventDefault()" @click="initCustomer(0)"><a-icon type="reload" /> 刷新列表</div>
                </div>
                <a-select-option v-for="(item,index) in cusList" :key="index" :value="item.id">
                  {{ item.supplier }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单据日期">
              <j-date v-decorator="['operTime', validatorRules.operTime]" :show-time="true"/>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="配货单编号">
              <a-input placeholder="请输入配货单编号" v-decorator.trim="[ 'number' ]" />
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="关联销售订单">
              <a-input placeholder="请输入销售订单号" v-decorator.trim="[ 'linkNumber' ]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="24">
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="配货状态">
              <a-select v-decorator="['pickingStatus']" placeholder="请选择配货状态" :disabled="action === 'edit'">
                <a-select-option value="0">未配货</a-select-option>
                <a-select-option value="1">配货中</a-select-option>
                <a-select-option value="2">已完成</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="配货人员">
              <j-select-multiple placeholder="请选择配货人员" v-model="personList.value" :options="personList.options"/>
            </a-form-item>
          </a-col>
          <a-col :lg="12" :md="24" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="备注">
              <a-input placeholder="请输入备注" v-decorator.trim="[ 'remark' ]" />
            </a-form-item>
          </a-col>
        </a-row>
        <j-editable-table id="pickingModal"
          :ref="refKeys[0]"
          :loading="materialTable.loading"
          :columns="materialTable.columns"
          :dataSource="materialTable.dataSource"
          :minWidth="minWidth"
          :maxHeight="300"
          :rowNumber="false"
          :rowSelection="true"
          :actionButton="rowCanEdit"
          :actionDeleteButton="!rowCanEdit"
          :dragSortAndNumber="rowCanEdit"
          @valueChange="onValueChange"
          @added="onAdded"
          @deleted="onDeleted">
          <template #buttonAfter>
            <a-row v-if="rowCanEdit" :gutter="24" style="float:left;padding-bottom: 5px;">
              <a-col v-if="scanStatus" :md="6" :sm="24">
                <a-button @click="scanEnter">扫码录入</a-button>
              </a-col>
              <a-col v-if="!scanStatus" :md="16" :sm="24" style="padding: 0 8px 0 12px">
                <a-input placeholder="请扫描商品条码" v-model="scanBarCode" @pressEnter="scanEnter" ref="scanBarCode"/>
              </a-col>
              <a-col v-if="!scanStatus" :md="4" :sm="24">
                <a-button @click="scanEnter">确定</a-button>
              </a-col>
              <a-col v-if="!scanStatus" :md="4" :sm="24">
                <a-button @click="scanCancel">取消</a-button>
              </a-col>
            </a-row>
          </template>
        </j-editable-table>
        <a-row class="form-row" :gutter="24">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="附件">
              <j-upload v-model="fileList" :number="3" :isDetail="readOnly"></j-upload>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <!-- 客户新增对话框 -->
    <customer-modal ref="customerModal" @ok="customerModalOk" @close="customerModalClose"></customer-modal>
  </j-modal>
</template>

<script>
  import { FormTypes } from '@/utils/JEditableTableUtil'
  import { JEditableTableMixin } from '@/mixins/JEditableTableMixin'
  import { BillModalMixin } from '../mixins/BillModalMixin'
  import { getMpListShort, handleIntroJs } from "@/utils/util"
  import JSelectMultiple from '@/components/jeecg/JSelectMultiple'
  import JUpload from '@/components/jeecg/JUpload'
  import JDate from '@/components/jeecg/JDate'
  import CustomerModal from '../../system/modules/CustomerModal'
  import Vue from 'vue'

  export default {
    name: "PickingModal",
    mixins: [JEditableTableMixin, BillModalMixin],
    components: {
      CustomerModal,
      JUpload,
      JDate,
      JSelectMultiple,
      VNodes: {
        functional: true,
        render: (h, ctx) => ctx.props.vnodes,
      }
    },
    data () {
      return {
        title:"操作",
        width: '1600px',
        moreStatus: false,
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        visible: false,
        operTimeStr: '',
        prefixNo: 'PHD',
        fileList:[],
        model: {},
        hasLinkedSaleBill: false, // 是否有关联的销售单
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules:{
          operTime:{
            rules: [
              { required: true, message: '请输入单据日期！' }
            ]
          },
          organId:{
            rules: [
              { required: true, message: '请选择客户！' }
            ]
          }
        },
        url: {
          add: '/pickingList/addPickingListWithDetail',
          edit: '/pickingList/updatePickingListWithDetail',
          detailList: '/depotItem/getDetailList',
          complete: '/pickingList/completePickingOrder',
          revert: '/pickingList/revertPickingOrder'
        }
      }
    },
    computed: {
      rowCanEdit() {
        return this.model.pickingStatus !== '2' // 已完成状态不可编辑
      }
    },
    created () {
    },
    methods: {

      // 完成配货
      handleComplete() {
        this.confirmLoading = true
        this.$http.post(this.url.complete, {id: this.model.id}).then((res) => {
          if (res.success) {
            this.$message.success('配货完成')
            this.model.pickingStatus = '2'
            this.form.setFieldsValue({pickingStatus: '2'})
          } else {
            this.$message.warning(res.message)
          }
          this.confirmLoading = false
        })
      },
      // 撤销完成
      handleRevert() {
        this.confirmLoading = true
        this.$http.post(this.url.revert, {id: this.model.id}).then((res) => {
          if (res.success) {
            this.$message.success('撤销完成成功')
            this.model.pickingStatus = '1'
            this.form.setFieldsValue({pickingStatus: '1'})
          } else {
            this.$message.warning(res.message)
          }
          this.confirmLoading = false
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
