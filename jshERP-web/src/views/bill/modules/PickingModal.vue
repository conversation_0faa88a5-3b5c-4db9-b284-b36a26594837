<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :keyboard="false"
    :forceRender="true"
    v-bind:prefixNo="prefixNo"
    fullscreen
    switchHelp
    switchFullscreen
    @cancel="handleCancel"
    :id="prefixNo"
    style="top:20px;height: 95%;">
    <template slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button v-if="billPrintFlag && isShowPrintBtn" @click="handlePrint('配货单')">打印配货单</a-button>
      <a-button v-if="model.pickingStatus === '1'" :loading="confirmLoading" @click="handleComplete">完成配货</a-button>
      <a-button v-if="model.pickingStatus === '2' && !hasLinkedSaleBill" :loading="confirmLoading" @click="handleRevert">撤销完成</a-button>
      <a-button type="primary" :loading="confirmLoading" @click="handleOk">保存（Ctrl+S）</a-button>
    </template>
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row class="form-row" :gutter="24">
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="客户">
              <a-select placeholder="请选择客户" v-decorator="[ 'organId', validatorRules.organId ]"
                :dropdownMatchSelectWidth="false" showSearch optionFilterProp="children" @change="handleOrganChange">
                <div slot="dropdownRender" slot-scope="menu">
                  <v-nodes :vnodes="menu" />
                  <a-divider style="margin: 4px 0;" />
                  <div v-if="quickBtn.customer" class="dropdown-btn" @mousedown="e => e.preventDefault()" @click="addCustomer"><a-icon type="plus" /> 新增客户</div>
                  <div class="dropdown-btn" @mousedown="e => e.preventDefault()" @click="initCustomer(0)"><a-icon type="reload" /> 刷新列表</div>
                </div>
                <a-select-option v-for="(item,index) in cusList" :key="index" :value="item.id">
                  {{ item.supplier }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="单据日期">
              <j-date v-decorator="['operTime', validatorRules.operTime]" :show-time="true"/>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="配货单编号">
              <a-input placeholder="请输入配货单编号" v-decorator.trim="[ 'number' ]" />
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="关联订单">
              <a-input-search placeholder="请选择关联销售订单" v-decorator="[ 'linkNumber' ]" @search="onSearchLinkNumber" :readOnly="true"/>
            </a-form-item>
          </a-col>
        </a-row>
        <j-editable-table id="pickingModal"
          :ref="refKeys[0]"
          :loading="materialTable.loading"
          :columns="materialTable.columns"
          :dataSource="materialTable.dataSource"
          :minWidth="minWidth"
          :maxHeight="300"
          :rowNumber="false"
          :rowSelection="true"
          :actionButton="rowCanEdit"
          :actionDeleteButton="!rowCanEdit"
          :dragSortAndNumber="rowCanEdit"
          @valueChange="onValueChange"
          @added="onAdded"
          @deleted="onDeleted">
          <template #buttonAfter>
            <a-row v-if="rowCanEdit" :gutter="24" style="float:left;padding-bottom: 5px;">
              <a-col v-if="scanStatus" :md="6" :sm="24">
                <a-button @click="scanEnter">扫码录入</a-button>
              </a-col>
              <a-col v-if="!scanStatus" :md="16" :sm="24" style="padding: 0 8px 0 12px">
                <a-input placeholder="请扫描商品条码并回车" v-model="scanBarCode" @pressEnter="scanPressEnter" ref="scanBarCode"/>
              </a-col>
              <a-col v-if="!scanStatus" :md="6" :sm="24" style="padding: 0px 12px 0 0">
                <a-button @click="stopScan">收起扫码</a-button>
              </a-col>
            </a-row>
          </template>
        </j-editable-table>
        <a-row class="form-row" :gutter="24">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="{xs: { span: 24 },sm: { span: 24 }}" label="">
              <a-textarea :rows="1" placeholder="请输入备注" v-decorator="[ 'remark' ]" style="margin-top:8px;"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="24">
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="配货人员">
              <j-select-multiple placeholder="请选择配货人员" v-model="personList.value" :options="personList.options"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="24">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="附件">
              <j-upload v-model="fileList" :number="3" :isDetail="readOnly"></j-upload>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <!-- 客户新增对话框 -->
    <customer-modal ref="customerModalForm" @ok="customerModalFormOk"></customer-modal>
    <!-- 关联订单选择对话框 -->
    <link-bill-list ref="linkBillList" @ok="linkBillListOk"></link-bill-list>
  </j-modal>
</template>

<script>
  import pick from 'lodash.pick'
  import { FormTypes } from '@/utils/JEditableTableUtil'
  import { JEditableTableMixin } from '@/mixins/JEditableTableMixin'
  import { BillModalMixin } from '../mixins/BillModalMixin'
  import { getMpListShort, handleIntroJs } from "@/utils/util"
  import JSelectMultiple from '@/components/jeecg/JSelectMultiple'
  import JUpload from '@/components/jeecg/JUpload'
  import JDate from '@/components/jeecg/JDate'
  import CustomerModal from '../../system/modules/CustomerModal'
  import LinkBillList from '../dialog/LinkBillList'
  import Vue from 'vue'

  export default {
    name: "PickingModal",
    mixins: [JEditableTableMixin, BillModalMixin],
    components: {
      CustomerModal,
      LinkBillList,
      JUpload,
      JDate,
      JSelectMultiple,
      VNodes: {
        functional: true,
        render: (h, ctx) => ctx.props.vnodes,
      }
    },
    data () {
      return {
        title:"操作",
        width: '1600px',
        moreStatus: false,
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        visible: false,
        operTimeStr: '',
        prefixNo: 'PHD',
        fileList:[],
        model: {},
        hasLinkedSaleBill: false, // 是否有关联的销售单
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        refKeys: ['materialDataTable'],
        activeKey: 'materialDataTable',
        confirmLoading: false,
        validatorRules:{
          operTime:{
            rules: [
              { required: true, message: '请输入单据日期！' }
            ]
          },
          organId:{
            rules: [
              { required: true, message: '请选择客户！' }
            ]
          }
        },
        materialTable: {
          loading: false,
          dataSource: [],
          columns: [
            { title: '', key: 'hiddenKey', width: '1%', type: FormTypes.hidden },
            { title: '条码', key: 'barCode', width: '15%', type: FormTypes.popupJsh, kind: 'material', multi: true,
              validateRules: [{ required: true, message: '${title}不能为空' }]
            },
            { title: '名称', key: 'name', width: '15%', type: FormTypes.normal },
            { title: '规格', key: 'standard', width: '12%', type: FormTypes.normal },
            { title: '单位', key: 'unit', width: '8%', type: FormTypes.normal },
            { title: '库存', key: 'stock', width: '8%', type: FormTypes.normal },
            { title: '仓位货架', key: 'rackNumber', width: '12%', type: FormTypes.normal },
            { title: '数量', key: 'operNumber', width: '10%', type: FormTypes.inputNumber, statistics: true,
              validateRules: [{ required: true, message: '${title}不能为空' }]
            },
            { title: '备注', key: 'remark', width: '15%', type: FormTypes.input }
          ]
        },
        minWidth: 800,
        url: {
          add: '/pickingList/addPickingListWithDetail',
          edit: '/pickingList/updatePickingListWithDetail',
          detailList: '/depotItem/getDetailList',
          complete: '/pickingList/completePickingOrder',
          revert: '/pickingList/revertPickingOrder'
        }
      }
    },
    computed: {
      rowCanEdit() {
        return this.model.pickingStatus !== '2' // 已完成状态不可编辑
      }
    },
    created () {
    },
    methods: {
      //调用完edit()方法之后会自动调用此方法
      editAfter() {
        if (this.action === 'add') {
          this.addInit(this.prefixNo)
          this.personList.value = ''
          this.fileList = []
        } else {
          this.model.operTime = this.model.operTimeStr
          this.personList.value = this.model.pickingPerson
          this.fileList = this.model.fileName
          this.$nextTick(() => {
            this.form.setFieldsValue(pick(this.model,'organId', 'operTime', 'number', 'linkNumber', 'remark', 'pickingStatus'))
          });
          // 加载子表数据
          let params = {
            headerId: this.model.id,
            mpList: getMpListShort(Vue.ls.get('materialPropertyList')),
            linkType: 'basic'
          }
          this.requestSubTableData(this.url.detailList, params, this.materialTable);
        }
        //复制新增单据-初始化单号和日期
        if(this.action === 'copyAdd') {
          this.model.id = ''
          this.model.tenantId = ''
          this.copyAddInit(this.prefixNo)
        }
        this.initCustomer(0)
        this.initSalesman()
        this.initQuickBtn()
      },

      // 获取表单数据
      getFormData() {
        let formData = this.form.getFieldsValue()
        let billMain = Object.assign({}, formData)
        if(this.model.id){
          billMain.id = this.model.id
        }
        billMain.pickingPerson = this.personList.value
        return {
          info: JSON.stringify(billMain),
          rows: JSON.stringify(this.materialTable.dataSource)
        }
      },

      // 关联订单搜索
      onSearchLinkNumber() {
        this.$refs.linkBillList.show('其它', '销售订单', '客户', "1,3")
        this.$refs.linkBillList.title = "请选择销售订单"
      },
      // 关联订单选择回调
      linkBillListOk(selectBillDetailRows, linkNumber, organId, discountMoney, deposit, remark, depotId, accountId, salesMan) {
        let that = this
        this.rowCanEdit = false
        this.materialTable.columns[1].type = FormTypes.normal
        this.materialTable.dataSource = []
        for (let i = 0; i < selectBillDetailRows.length; i++) {
          let item = selectBillDetailRows[i]
          this.materialTable.dataSource.push({
            id: item.id,
            barCode: item.barCode,
            name: item.name,
            standard: item.standard,
            unit: item.unit,
            stock: item.stock,
            rackNumber: item.rackNumber,
            operNumber: item.operNumber,
            remark: item.remark
          })
        }
        this.$nextTick(() => {
          this.form.setFieldsValue({
            'organId': organId,
            'linkNumber': linkNumber,
            'remark': remark
          })
        })
      },

      // 完成配货
      handleComplete() {
        this.confirmLoading = true
        this.$http.post(this.url.complete, {id: this.model.id}).then((res) => {
          if (res.success) {
            this.$message.success('配货完成')
            this.model.pickingStatus = '2'
            this.form.setFieldsValue({pickingStatus: '2'})
          } else {
            this.$message.warning(res.message)
          }
          this.confirmLoading = false
        })
      },
      // 撤销完成
      handleRevert() {
        this.confirmLoading = true
        this.$http.post(this.url.revert, {id: this.model.id}).then((res) => {
          if (res.success) {
            this.$message.success('撤销完成成功')
            this.model.pickingStatus = '1'
            this.form.setFieldsValue({pickingStatus: '1'})
          } else {
            this.$message.warning(res.message)
          }
          this.confirmLoading = false
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
