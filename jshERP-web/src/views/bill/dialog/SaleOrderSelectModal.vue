<template>
  <a-modal
    title="选择销售订单"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <div>
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline" @keyup.enter.native="searchQuery">
          <a-row :gutter="24">
            <a-col :md="8" :sm="24">
              <a-form-item label="单据编号">
                <a-input placeholder="请输入销售订单编号" v-model="queryParam.number"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="客户">
                <a-select placeholder="请选择客户" showSearch allow-clear optionFilterProp="children" v-model="queryParam.organId">
                  <a-select-option v-for="(item,index) in cusList" :key="index" :value="item.id">
                    {{ item.supplier }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item>
                <a-button type="primary" @click="searchQuery">查询</a-button>
                <a-button style="margin-left: 8px" @click="searchReset">重置</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      
      <!-- 表格区域 -->
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
        <span slot="customRenderStatus" slot-scope="text">
          <a-tag v-if="text === '0'" color="red">未审核</a-tag>
          <a-tag v-else-if="text === '1'" color="green">已审核</a-tag>
          <a-tag v-else-if="text === '2'" color="blue">完成销售</a-tag>
          <a-tag v-else-if="text === '3'" color="orange">部分销售</a-tag>
          <a-tag v-else color="gray">未知</a-tag>
        </span>
      </a-table>
    </div>
  </a-modal>
</template>

<script>
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { getAction } from '@/api/manage'

  export default {
    name: "SaleOrderSelectModal",
    mixins: [JeecgListMixin],
    data() {
      return {
        visible: false,
        confirmLoading: false,
        selectedRowKeys: [],
        selectedRows: [],
        cusList: [],
        queryParam: {
          number: "",
          organId: undefined,
          type: "其它",
          subType: "销售订单",
          status: "1" // 只显示已审核的订单
        },
        columns: [
          { title: '单据编号', dataIndex: 'number', width: 130 },
          { title: '客户', dataIndex: 'organName', width: 120 },
          { title: '单据日期', dataIndex: 'operTimeStr', width: 100 },
          { title: '商品信息', dataIndex: 'materialsList', width: 300 },
          { title: '商品数量', dataIndex: 'materialCount', width: 80 },
          { title: '合计金额', dataIndex: 'totalPrice', width: 100 },
          { title: '操作员', dataIndex: 'userName', width: 80 },
          { title: '状态', dataIndex: 'status', width: 80, align: "center",
            scopedSlots: { customRender: 'customRenderStatus' }
          }
        ],
        url: {
          list: "/depotHead/list"
        }
      }
    },
    created() {
      this.initCustomer()
    },
    methods: {
      show() {
        this.visible = true
        this.selectedRowKeys = []
        this.selectedRows = []
        this.loadData(1)
      },
      
      handleOk() {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请选择一个销售订单！')
          return
        }
        
        const selectedOrder = this.selectedRows[0]
        this.$emit('ok', selectedOrder)
        this.handleCancel()
      },
      
      handleCancel() {
        this.visible = false
        this.selectedRowKeys = []
        this.selectedRows = []
      },
      
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      
      searchQuery() {
        this.loadData(1)
      },
      
      searchReset() {
        this.queryParam = {
          number: "",
          organId: undefined,
          type: "其它",
          subType: "销售订单",
          status: "1"
        }
        this.loadData(1)
      },
      
      // 初始化客户列表
      initCustomer() {
        getAction('/supplier/list', {
          type: '客户',
          status: '0'
        }).then((res) => {
          if (res.success) {
            this.cusList = res.result.records || res.result
          }
        })
      }
    }
  }
</script>

<style scoped>
  .table-page-search-wrapper {
    padding: 16px;
    background: #fafafa;
    margin-bottom: 16px;
  }
</style>
