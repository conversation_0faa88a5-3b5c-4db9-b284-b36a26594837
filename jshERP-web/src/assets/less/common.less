/*列表上方操作按钮区域*/
.ant-card-body .table-operator {
  margin-bottom: 0px;
}
/** Button按钮间距 */
.table-operator .ant-btn {
  margin: 0 8px 8px 0;
}
.table-operator .ant-btn-group .ant-btn {
  margin: 0;
}
.table-operator .ant-btn-group .ant-btn:last-child {
  margin: 0 8px 8px 0;
}
/*列表td的padding设置 可以控制列表大小*/
.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}
.depot-mask {
  margin-top: 93px;
  margin-left: 154px;
}
/*列表页面弹出modal*/
.ant-modal-cust-warp {
  height: 100%
}
/*弹出modal Y轴滚动条*/
.ant-modal-cust-warp .ant-modal-body {
  padding: 24px 24px 12px 24px;
  height: calc(100% - 110px) !important;
  overflow-y: auto
}
/*弹出modal 先有content后有body 故滚动条控制在body上*/
.ant-modal-cust-warp .ant-modal-content {
  height: 90%;
  overflow-y: hidden
}
/*文本框样式*/
.ant-modal-cust-warp .ant-form-item {
  margin-bottom: 12px;
}
/*全屏模式*/
.ant-modal-cust-warp .fullscreen >.ant-modal-content {
  height: 100vh;
  border-radius: 0;
}
/*全屏模式*/
.ant-modal-cust-warp .fullscreen >.ant-modal-content >.ant-modal-body {
  padding: 24px 24px 12px 24px;
  height: calc(100% - 200px) !important;
  overflow-y: auto
}
/*列表中有图片的加这个样式 参考用户管理*/
.anty-img-wrap {
  height: 25px;
  position: relative;
}
.anty-img-wrap > img {
  max-height: 100%;
}
/*列表中范围查询样式*/
.query-group-cust{width: calc(50% - 10px)}
.query-group-split-cust:before{content:"~";width: 20px;display: inline-block;text-align: center}
/*erp风格子表外框padding设置*/
.ant-card-wider-padding.cust-erp-sub-tab>.ant-card-body{padding:5px 12px}
/* 内嵌子表背景颜色 */
.j-inner-table-wrapper /deep/ .ant-table-expanded-row .ant-table-wrapper .ant-table-tbody .ant-table-row {
  background-color: #FFFFFF;
}

.ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.1) !important;
}
/* 拖拽 */
.table-draggable-handle {
  /* width: 10px !important; */
  height: 100% !important;
  left: auto !important;
  right: -5px;
  cursor: col-resize;
  touch-action: none;
  border: none;
  position: absolute;
  transform: none !important;
  bottom: 0;
}
.resize-table-th {
  position: relative;
}
/* 单据下拉框-按钮 */
.dropdown-btn {
  float:left;
  padding: 4px 8px;
  cursor: pointer;
}