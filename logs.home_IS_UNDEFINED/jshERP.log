2025/07/08-00:10:52 INFO  [main] com.jsh.erp.ErpApplication - Starting ErpApplication on FRIEREN.station with PID 24108 (/Users/<USER>/IdeaProjects/jshERP/jshERP-boot/target/classes started by wanghai<PERSON><PERSON> in /Users/<USER>/IdeaProjects/jshERP)
2025/07/08-00:10:52 DEBUG [main] com.jsh.erp.ErpApplication - Running with Spring Boot v2.0.0.RELEASE, Spring v5.0.4.RELEASE
2025/07/08-00:10:52 INFO  [main] com.jsh.erp.ErpApplication - No active profile set, falling back to default profiles: default
2025/07/08-00:10:55 INFO  [main] com.jsh.erp.ErpApplication - Started ErpApplication in 2.406 seconds (JVM running for 2.614)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==>  Preparing: SELECT id, platform_key, platform_key_info, platform_value FROM jsh_platform_config WHERE (platform_key = ?) 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==> Parameters: platform_name(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==>  Preparing: SELECT id, platform_key, platform_key_info, platform_value FROM jsh_platform_config WHERE (platform_key = ?) 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==> Parameters: platform_url(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 10(String), RoleFunctions(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 9
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 10(String), RoleFunctions(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0401(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 2
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 040102(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 040104(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0502(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 4
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 050203(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 050202(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 050201(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 050204(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0603(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 4
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 060301(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 060302(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 060303(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 060305(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0801(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 5
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080103(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080105(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080107(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080109(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080111(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0704(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 6
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070402(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070403(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070404(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070405(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070406(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070407(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0301(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 14
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030113(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030102(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030105(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030103(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030104(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030106(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030107(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030150(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030108(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030109(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030101(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030110(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030111(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030112(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0101(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 4
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010101(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010102(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010103(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010105(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0102(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 7
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 01020101(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 01020102(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 01020103(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010202(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010204(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010205(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010206(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0001(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 10
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000102(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000103(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000108(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000104(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000106(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000105(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000105(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000109(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000107(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000112(String), 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 10(String), RoleFunctions(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (delete_flag <> ?) 
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:01 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 65
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==>  Preparing: SELECT id, platform_key, platform_key_info, platform_value FROM jsh_platform_config WHERE (platform_key = ?) 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==> Parameters: pay_fee_url(String)
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.MsgMapper.selectByExample - ==>  Preparing: SELECT id, msg_title, msg_content, create_time, type, user_id, status, tenant_id, delete_Flag FROM jsh_msg WHERE jsh_msg.tenant_id = 63 AND (status = ? AND user_id = ? AND delete_Flag <> ?) ORDER BY id DESC 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.MsgMapper.selectByExample - ==> Parameters: 1(String), 63(Long), 1(String)
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.RoleMapperEx.selectByConditionRole_COUNT - ==>  Preparing: SELECT count(0) FROM jsh_role WHERE jsh_role.tenant_id = 63 AND 1 = 1 AND ifnull(delete_flag, '0') != '1' 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.RoleMapperEx.selectByConditionRole_COUNT - ==> Parameters: 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg_COUNT - ==>  Preparing: SELECT count(0) FROM jsh_msg WHERE jsh_msg.tenant_id = 63 AND 1 = 1 AND ifnull(delete_Flag, '0') != '1' AND user_id = ? 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg_COUNT - ==> Parameters: 63(Long)
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.MsgMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.RoleMapperEx.selectByConditionRole_COUNT - <==      Total: 1
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg_COUNT - <==      Total: 1
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg - ==>  Preparing: SELECT * FROM jsh_msg WHERE jsh_msg.tenant_id = 63 AND 1 = 1 AND ifnull(delete_Flag, '0') != '1' AND user_id = ? ORDER BY create_time DESC LIMIT ? 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.RoleMapperEx.selectByConditionRole - ==>  Preparing: SELECT * FROM jsh_role WHERE jsh_role.tenant_id = 63 AND 1 = 1 AND ifnull(delete_flag, '0') != '1' ORDER BY sort ASC, id DESC LIMIT ? 
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg - ==> Parameters: 63(Long), 5(Integer)
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.RoleMapperEx.selectByConditionRole - ==> Parameters: 10(Integer)
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.RoleMapperEx.selectByConditionRole - <==      Total: 2
2025/07/08-00:11:02 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg - <==      Total: 1
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserCustomer(String)
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND (status = ? AND delete_flag <> ?) 
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - ==> Parameters: 0(Byte), 1(String)
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - <==      Total: 2
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==>  Preparing: SELECT id, supplier, contacts, phone_num, email, description, isystem, type, enabled, advance_in, begin_need_get, begin_need_pay, all_need_get, all_need_pay, fax, telephone, address, tax_num, bank_name, account_number, tax_rate, sort, creator, tenant_id, delete_flag FROM jsh_supplier WHERE jsh_supplier.tenant_id = 63 AND (type LIKE ? AND enabled = ? AND delete_flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==> Parameters: 客户(String), true(Boolean), 1(String)
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - <==      Total: 3
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:06 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==>  Preparing: SELECT id, platform_key, platform_key_info, platform_value FROM jsh_platform_config WHERE (platform_key = ?) 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==> Parameters: bill_excel_url(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserCustomer(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND (status = ? AND delete_flag <> ?) 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - ==> Parameters: 0(Byte), 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - <==      Total: 2
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==>  Preparing: SELECT id, supplier, contacts, phone_num, email, description, isystem, type, enabled, advance_in, begin_need_get, begin_need_pay, all_need_get, all_need_pay, fax, telephone, address, tax_num, bank_name, account_number, tax_rate, sort, creator, tenant_id, delete_flag FROM jsh_supplier WHERE jsh_supplier.tenant_id = 63 AND (type LIKE ? AND enabled = ? AND delete_flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==> Parameters: 客户(String), true(Boolean), 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - <==      Total: 3
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.DepotMapper.selectByExample - ==>  Preparing: SELECT id, name, address, warehousing, truckage, type, sort, remark, principal, enabled, tenant_id, delete_Flag, is_default FROM jsh_depot WHERE jsh_depot.tenant_id = 63 AND (type = ? AND enabled = ? AND delete_Flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.DepotMapper.selectByExample - ==> Parameters: 0(Integer), true(Boolean), 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.DepotMapper.selectByExample - <==      Total: 3
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - ==>  Preparing: select * from jsh_role where 1=1 and ifnull(delete_flag,'0') !='1' and id=? 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - ==> Parameters: 10(Long)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - ==>  Preparing: select * from jsh_role where 1=1 and ifnull(delete_flag,'0') !='1' and id=? 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - ==> Parameters: 10(Long)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserCustomer(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==>  Preparing: SELECT id, supplier, contacts, phone_num, email, description, isystem, type, enabled, advance_in, begin_need_get, begin_need_pay, all_need_get, all_need_pay, fax, telephone, address, tax_num, bank_name, account_number, tax_rate, sort, creator, tenant_id, delete_flag FROM jsh_supplier WHERE jsh_supplier.tenant_id = 63 AND (type LIKE ? AND enabled = ? AND delete_flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==> Parameters: 客户(String), true(Boolean), 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - <==      Total: 3
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.PersonMapper.selectByExample - ==>  Preparing: SELECT id, type, name, enabled, sort, tenant_id, delete_flag FROM jsh_person WHERE jsh_person.tenant_id = 63 AND (enabled = ? AND delete_flag <> ?) 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.PersonMapper.selectByExample - ==> Parameters: true(Boolean), 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.PersonMapper.selectByExample - <==      Total: 4
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.AccountMapper.selectByExample - ==>  Preparing: SELECT id, name, serial_no, initial_amount, current_amount, remark, enabled, sort, is_default, tenant_id, delete_flag FROM jsh_account WHERE jsh_account.tenant_id = 63 AND (enabled = ? AND delete_flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.AccountMapper.selectByExample - ==> Parameters: true(Boolean), 1(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.AccountMapper.selectByExample - <==      Total: 2
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.DepotHeadMapperEx.selectByConditionDepotHead_COUNT - ==>  Preparing: SELECT count(0) FROM (SELECT dh.id FROM jsh_depot_head dh LEFT JOIN jsh_depot_item di ON di.tenant_id = 63 AND dh.id = di.header_id AND ifnull(di.delete_flag, '0') != '1' LEFT JOIN jsh_material m ON m.tenant_id = 63 AND di.material_id = m.id AND ifnull(m.delete_flag, '0') != '1' LEFT JOIN jsh_material_extend me ON me.tenant_id = 63 AND di.material_extend_id = me.id AND ifnull(me.delete_flag, '0') != '1' WHERE dh.tenant_id = 63 AND 1 = 1 AND dh.type = ? AND dh.sub_type = ? AND dh.oper_time >= ? AND dh.oper_time <= ? AND dh.organ_id IN (?, ?, ?) AND ifnull(dh.delete_flag, '0') != '1' GROUP BY dh.id) tb LEFT JOIN jsh_depot_head jdh ON jdh.tenant_id = 63 AND jdh.id = tb.id AND ifnull(jdh.delete_flag, '0') != '1' LEFT JOIN jsh_supplier s ON s.tenant_id = 63 AND jdh.organ_id = s.id AND ifnull(s.delete_flag, '0') != '1' LEFT JOIN jsh_user u ON u.tenant_id = 63 AND jdh.creator = u.id LEFT JOIN jsh_account a ON a.tenant_id = 63 AND jdh.account_id = a.id AND ifnull(a.delete_flag, '0') != '1' 
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.DepotHeadMapperEx.selectByConditionDepotHead_COUNT - ==> Parameters: 其它(String), 销售订单(String), 2025-04-08 00:00:00(String), 2025-07-08 23:59:59(String), 71(String), 59(String), 58(String)
2025/07/08-00:11:27 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.DepotHeadMapperEx.selectByConditionDepotHead_COUNT - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==>  Preparing: SELECT id, platform_key, platform_key_info, platform_value FROM jsh_platform_config WHERE (platform_key = ?) 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==> Parameters: platform_name(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==>  Preparing: SELECT id, platform_key, platform_key_info, platform_value FROM jsh_platform_config WHERE (platform_key = ?) 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==> Parameters: platform_url(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 10(String), RoleFunctions(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 9
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 10(String), RoleFunctions(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0401(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 2
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 040102(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 040104(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0502(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 4
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 050203(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 050202(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 050201(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 050204(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0603(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 4
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 060301(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 060302(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 060303(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 060305(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0801(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 5
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080103(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080105(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080107(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080109(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 080111(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0704(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 6
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070402(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070403(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070404(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070405(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070406(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 070407(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0301(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 14
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030113(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030102(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030105(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030103(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030104(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030106(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030107(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030150(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030108(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030109(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030101(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030110(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030111(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 030112(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0101(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 4
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010101(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010102(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010103(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010105(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0102(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 7
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 01020101(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 01020102(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 01020103(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010202(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010204(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010205(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 010206(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 0001(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 10
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000102(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000103(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000108(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000104(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000106(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000105(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000105(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000109(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000107(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (enabled = ? AND parent_number = ? AND delete_flag <> ?) ORDER BY Sort 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: true(Boolean), 000112(String), 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 10(String), RoleFunctions(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==>  Preparing: SELECT id, number, name, parent_number, url, component, state, sort, enabled, type, push_btn, icon, delete_flag FROM jsh_function WHERE (delete_flag <> ?) 
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:36 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.FunctionMapper.selectByExample - <==      Total: 65
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==>  Preparing: SELECT id, platform_key, platform_key_info, platform_value FROM jsh_platform_config WHERE (platform_key = ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==> Parameters: bill_excel_url(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==>  Preparing: SELECT id, platform_key, platform_key_info, platform_value FROM jsh_platform_config WHERE (platform_key = ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - ==> Parameters: pay_fee_url(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-5] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.PlatformConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - ==>  Preparing: select * from jsh_role where 1=1 and ifnull(delete_flag,'0') !='1' and id=? 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - ==> Parameters: 10(Long)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg_COUNT - ==>  Preparing: SELECT count(0) FROM jsh_msg WHERE jsh_msg.tenant_id = 63 AND 1 = 1 AND ifnull(delete_Flag, '0') != '1' AND user_id = ? 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg_COUNT - ==> Parameters: 63(Long)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg_COUNT - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg - ==>  Preparing: SELECT * FROM jsh_msg WHERE jsh_msg.tenant_id = 63 AND 1 = 1 AND ifnull(delete_Flag, '0') != '1' AND user_id = ? ORDER BY create_time DESC LIMIT ? 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg - ==> Parameters: 63(Long), 5(Integer)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-8] com.jsh.erp.datasource.mappers.MsgMapperEx.selectByConditionMsg - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-2] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserRole(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - ==>  Preparing: select * from jsh_role where 1=1 and ifnull(delete_flag,'0') !='1' and id=? 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserCustomer(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - ==> Parameters: 10(Long)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND (status = ? AND delete_flag <> ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - ==> Parameters: 0(Byte), 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.RoleMapperEx.getRoleWithoutTenant - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-6] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - <==      Total: 2
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.DepotMapper.selectByExample - ==>  Preparing: SELECT id, name, address, warehousing, truckage, type, sort, remark, principal, enabled, tenant_id, delete_Flag, is_default FROM jsh_depot WHERE jsh_depot.tenant_id = 63 AND (type = ? AND enabled = ? AND delete_Flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.DepotMapper.selectByExample - ==> Parameters: 0(Integer), true(Boolean), 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==>  Preparing: SELECT id, supplier, contacts, phone_num, email, description, isystem, type, enabled, advance_in, begin_need_get, begin_need_pay, all_need_get, all_need_pay, fax, telephone, address, tax_num, bank_name, account_number, tax_rate, sort, creator, tenant_id, delete_flag FROM jsh_supplier WHERE jsh_supplier.tenant_id = 63 AND (type LIKE ? AND enabled = ? AND delete_flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==> Parameters: 客户(String), true(Boolean), 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.DepotMapper.selectByExample - <==      Total: 3
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - <==      Total: 3
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-1] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-10] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND id = ? 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - ==> Parameters: 63(Long)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserMapper.selectByPrimaryKey - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserCustomer(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.MsgMapper.selectByExample - ==>  Preparing: SELECT id, msg_title, msg_content, create_time, type, user_id, status, tenant_id, delete_Flag FROM jsh_msg WHERE jsh_msg.tenant_id = 63 AND (status = ? AND user_id = ? AND delete_Flag <> ?) ORDER BY id DESC 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.MsgMapper.selectByExample - ==> Parameters: 1(String), 63(Long), 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-3] com.jsh.erp.datasource.mappers.MsgMapper.selectByExample - <==      Total: 0
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==>  Preparing: SELECT id, supplier, contacts, phone_num, email, description, isystem, type, enabled, advance_in, begin_need_get, begin_need_pay, all_need_get, all_need_pay, fax, telephone, address, tax_num, bank_name, account_number, tax_rate, sort, creator, tenant_id, delete_flag FROM jsh_supplier WHERE jsh_supplier.tenant_id = 63 AND (type LIKE ? AND enabled = ? AND delete_flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==> Parameters: 客户(String), true(Boolean), 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - <==      Total: 3
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.PersonMapper.selectByExample - ==>  Preparing: SELECT id, type, name, enabled, sort, tenant_id, delete_flag FROM jsh_person WHERE jsh_person.tenant_id = 63 AND (enabled = ? AND delete_flag <> ?) 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.PersonMapper.selectByExample - ==> Parameters: true(Boolean), 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.PersonMapper.selectByExample - <==      Total: 4
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.AccountMapper.selectByExample - ==>  Preparing: SELECT id, name, serial_no, initial_amount, current_amount, remark, enabled, sort, is_default, tenant_id, delete_flag FROM jsh_account WHERE jsh_account.tenant_id = 63 AND (enabled = ? AND delete_flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.AccountMapper.selectByExample - ==> Parameters: true(Boolean), 1(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.AccountMapper.selectByExample - <==      Total: 2
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.DepotHeadMapperEx.selectByConditionDepotHead_COUNT - ==>  Preparing: SELECT count(0) FROM (SELECT dh.id FROM jsh_depot_head dh LEFT JOIN jsh_depot_item di ON di.tenant_id = 63 AND dh.id = di.header_id AND ifnull(di.delete_flag, '0') != '1' LEFT JOIN jsh_material m ON m.tenant_id = 63 AND di.material_id = m.id AND ifnull(m.delete_flag, '0') != '1' LEFT JOIN jsh_material_extend me ON me.tenant_id = 63 AND di.material_extend_id = me.id AND ifnull(me.delete_flag, '0') != '1' WHERE dh.tenant_id = 63 AND 1 = 1 AND dh.type = ? AND dh.sub_type = ? AND dh.oper_time >= ? AND dh.oper_time <= ? AND dh.organ_id IN (?, ?, ?) AND ifnull(dh.delete_flag, '0') != '1' GROUP BY dh.id) tb LEFT JOIN jsh_depot_head jdh ON jdh.tenant_id = 63 AND jdh.id = tb.id AND ifnull(jdh.delete_flag, '0') != '1' LEFT JOIN jsh_supplier s ON s.tenant_id = 63 AND jdh.organ_id = s.id AND ifnull(s.delete_flag, '0') != '1' LEFT JOIN jsh_user u ON u.tenant_id = 63 AND jdh.creator = u.id LEFT JOIN jsh_account a ON a.tenant_id = 63 AND jdh.account_id = a.id AND ifnull(a.delete_flag, '0') != '1' 
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.DepotHeadMapperEx.selectByConditionDepotHead_COUNT - ==> Parameters: 其它(String), 销售订单(String), 2025-04-08 00:00:00(String), 2025-07-08 23:59:59(String), 71(String), 59(String), 58(String)
2025/07/08-00:11:37 DEBUG [http-nio-9999-exec-7] com.jsh.erp.datasource.mappers.DepotHeadMapperEx.selectByConditionDepotHead_COUNT - <==      Total: 1
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==>  Preparing: select * from jsh_user_business where key_id=? and type=? and ifnull(delete_flag,'0') !='1' 
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - ==>  Preparing: SELECT id, username, login_name, password, leader_flag, position, department, email, phonenum, ismanager, isystem, status, description, remark, weixin_open_id, tenant_id, delete_flag FROM jsh_user WHERE jsh_user.tenant_id = 63 AND (status = ? AND delete_flag <> ?) 
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - ==> Parameters: 63(String), UserCustomer(String)
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - ==> Parameters: 0(Byte), 1(String)
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-9] com.jsh.erp.datasource.mappers.UserMapper.selectByExample - <==      Total: 2
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.UserBusinessMapperEx.getBasicDataByKeyIdAndType - <==      Total: 1
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==>  Preparing: SELECT id, supplier, contacts, phone_num, email, description, isystem, type, enabled, advance_in, begin_need_get, begin_need_pay, all_need_get, all_need_pay, fax, telephone, address, tax_num, bank_name, account_number, tax_rate, sort, creator, tenant_id, delete_flag FROM jsh_supplier WHERE jsh_supplier.tenant_id = 63 AND (type LIKE ? AND enabled = ? AND delete_flag <> ?) ORDER BY sort ASC, id DESC 
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - ==> Parameters: 客户(String), true(Boolean), 1(String)
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.SupplierMapper.selectByExample - <==      Total: 3
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==>  Preparing: SELECT id, company_name, company_contacts, company_address, company_tel, company_fax, company_post_code, sale_agreement, depot_flag, customer_flag, minus_stock_flag, purchase_by_sale_flag, multi_level_approval_flag, multi_bill_type, force_approval_flag, update_unit_price_flag, over_link_bill_flag, in_out_manage_flag, multi_account_flag, move_avg_price_flag, audit_print_flag, zero_change_amount_flag, customer_static_price_flag, tenant_id, delete_flag FROM jsh_system_config WHERE jsh_system_config.tenant_id = 63 AND (delete_flag <> ?) 
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - ==> Parameters: 1(String)
2025/07/08-00:12:04 DEBUG [http-nio-9999-exec-4] com.jsh.erp.datasource.mappers.SystemConfigMapper.selectByExample - <==      Total: 1
